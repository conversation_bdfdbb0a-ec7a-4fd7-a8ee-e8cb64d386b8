{"rustc": 15497389221046826682, "features": "[\"alloc\", \"default\", \"fs\", \"std\"]", "declared_features": "[\"all-apis\", \"alloc\", \"compiler_builtins\", \"core\", \"default\", \"event\", \"fs\", \"io_uring\", \"libc\", \"libc_errno\", \"linux_4_11\", \"linux_5_1\", \"linux_5_11\", \"linux_latest\", \"mm\", \"mount\", \"net\", \"param\", \"pipe\", \"process\", \"pty\", \"rand\", \"runtime\", \"rustc-dep-of-std\", \"rustc-std-workspace-alloc\", \"shm\", \"std\", \"stdio\", \"system\", \"termios\", \"thread\", \"time\", \"try_close\", \"use-explicitly-provided-auxv\", \"use-libc\", \"use-libc-auxv\"]", "target": 16221545317719767766, "profile": 2734000389832276527, "path": 7554643852353655800, "deps": [[7896293946984509699, "bitflags", false, 5474438432958757285], [9559541369283268958, "libc", false, 16168465844030790991], [12053020504183902936, "build_script_build", false, 3334796033968539278], [14633813869673313769, "libc_errno", false, 9962236522681907254]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rustix-c4852ac3ca24130d/dep-lib-rustix", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}