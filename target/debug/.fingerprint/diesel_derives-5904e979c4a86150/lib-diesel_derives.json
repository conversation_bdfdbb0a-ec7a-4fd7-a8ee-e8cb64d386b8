{"rustc": 15497389221046826682, "features": "[\"32-column-tables\", \"default\", \"r2d2\", \"sqlite\", \"with-deprecated\"]", "declared_features": "[\"128-column-tables\", \"32-column-tables\", \"64-column-tables\", \"chrono\", \"default\", \"mysql\", \"nightly\", \"postgres\", \"r2d2\", \"sqlite\", \"time\", \"with-deprecated\", \"without-deprecated\"]", "target": 14327538309307208008, "profile": 3033921117576893, "path": 15081903622923879380, "deps": [[3060637413840920116, "proc_macro2", false, 7608282993562232869], [9431064406736484046, "dsl_auto_type", false, 14609639390523473794], [10640660562325816595, "syn", false, 9852450757901358922], [12821317385041146439, "diesel_table_macro_syntax", false, 4883335948346480609], [17990358020177143287, "quote", false, 6760751990977140778]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/diesel_derives-5904e979c4a86150/dep-lib-diesel_derives", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}