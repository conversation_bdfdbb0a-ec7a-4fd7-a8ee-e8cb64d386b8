{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"nightly\"]", "target": 16297729212658632601, "profile": 5347358027863023418, "path": 6535723324991939796, "deps": [[2828590642173593838, "cfg_if", false, 1880661156984210890], [3016941897346161952, "downcast", false, 9658701701440346087], [7886665781035375288, "fragile", false, 1689129216436380303], [9001202093189684693, "mockall_derive", false, 7407421167868086862], [12516616738327129663, "predicates_tree", false, 13803223148634011149], [15863765456528386755, "predicates", false, 6379241567888320684]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/mockall-b5852965b3c24a37/dep-lib-mockall", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}