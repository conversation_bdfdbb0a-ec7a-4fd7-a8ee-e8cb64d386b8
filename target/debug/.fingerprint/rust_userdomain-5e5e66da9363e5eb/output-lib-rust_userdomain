{"$message_type": "diagnostic", "message": "unused variable: `floor_id`", "code": {"code": "unused_variables", "explanation": null}, "level": "warning", "spans": [{"file_name": "userdomain_rust/rust_userdomain/src/server_apis/family_apis/operate_floor_api.rs", "byte_start": 725, "byte_end": 733, "line_start": 26, "line_end": 26, "column_start": 15, "column_end": 23, "is_primary": true, "text": [{"text": "    if let Ok(floor_id) = result {", "highlight_start": 15, "highlight_end": 23}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}], "children": [{"message": "`#[warn(unused_variables)]` on by default", "code": null, "level": "note", "spans": [], "children": [], "rendered": null}, {"message": "if this is intentional, prefix it with an underscore", "code": null, "level": "help", "spans": [{"file_name": "userdomain_rust/rust_userdomain/src/server_apis/family_apis/operate_floor_api.rs", "byte_start": 725, "byte_end": 733, "line_start": 26, "line_end": 26, "column_start": 15, "column_end": 23, "is_primary": true, "text": [{"text": "    if let Ok(floor_id) = result {", "highlight_start": 15, "highlight_end": 23}], "label": null, "suggested_replacement": "_floor_id", "suggestion_applicability": "MaybeIncorrect", "expansion": null}], "children": [], "rendered": null}], "rendered": "\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `floor_id`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/src/server_apis/family_apis/operate_floor_api.rs:26:15\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m26\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    if let Ok(floor_id) = result {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_floor_id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}