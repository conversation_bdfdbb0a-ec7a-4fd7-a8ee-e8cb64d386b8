{"rustc": 15497389221046826682, "features": "[\"color\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\", \"wrap_help\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-doc\", \"unstable-ext\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 6917651628887788201, "profile": 9589772125425470163, "path": 13671293261956799529, "deps": [[5820056977320921005, "anstream", false, 16134819161033136730], [6702271452560637055, "terminal_size", false, 485389663609390153], [9394696648929125047, "anstyle", false, 12996159999018373847], [11166530783118767604, "strsim", false, 8623092587096170264], [11649982696571033535, "clap_lex", false, 12149675052412702937]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/clap_builder-e4759dbe8e15c9ef/dep-lib-clap_builder", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}