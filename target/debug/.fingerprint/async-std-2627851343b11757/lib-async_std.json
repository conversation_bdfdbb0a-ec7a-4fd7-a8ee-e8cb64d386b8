{"rustc": 15497389221046826682, "features": "[\"alloc\", \"async-channel\", \"async-global-executor\", \"async-io\", \"async-lock\", \"crossbeam-utils\", \"default\", \"futures-channel\", \"futures-core\", \"futures-io\", \"futures-lite\", \"gloo-timers\", \"kv-log-macro\", \"log\", \"memchr\", \"once_cell\", \"pin-project-lite\", \"pin-utils\", \"slab\", \"std\", \"wasm-bindgen-futures\"]", "declared_features": "[\"alloc\", \"async-attributes\", \"async-channel\", \"async-global-executor\", \"async-io\", \"async-lock\", \"async-process\", \"attributes\", \"crossbeam-utils\", \"default\", \"docs\", \"futures-channel\", \"futures-core\", \"futures-io\", \"futures-lite\", \"gloo-timers\", \"io_safety\", \"kv-log-macro\", \"log\", \"memchr\", \"once_cell\", \"pin-project-lite\", \"pin-utils\", \"slab\", \"std\", \"surf\", \"tokio02\", \"tokio03\", \"tokio1\", \"unstable\", \"wasm-bindgen-futures\"]", "target": 9139776409365598091, "profile": 5347358027863023418, "path": 11631313608246733686, "deps": [[5103565458935487, "futures_io", false, 13437308025047373296], [385810070298638530, "log", false, 9570944178305552605], [1615478164327904835, "pin_utils", false, 1866736041211598894], [1906322745568073236, "pin_project_lite", false, 14947904571586367982], [4468123440088164316, "crossbeam_utils", false, 3756119405939413192], [5070769681332304831, "once_cell", false, 15305827305686893015], [5195813957092839672, "async_lock", false, 321830307763468518], [5302544599749092241, "async_channel", false, 17658678382314929577], [6955678925937229351, "slab", false, 3040466203498628111], [7425331225454150061, "futures_lite", false, 15747117361393556083], [7620660491849607393, "futures_core", false, 9777933496889008138], [13330646740533913557, "async_global_executor", false, 10834847819492272508], [14383121809127811842, "async_io", false, 11639556083055136085], [15932120279885307830, "memchr", false, 8944879343585981577], [17569958903244628888, "kv_log_macro", false, 10545803013728606630]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/async-std-2627851343b11757/dep-lib-async_std", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}