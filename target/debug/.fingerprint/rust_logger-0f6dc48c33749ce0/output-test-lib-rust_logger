{"$message_type":"diagnostic","message":"unused import: `core::mem`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"logger/rust_logger/src/generated/logger_generated.rs","byte_start":91,"byte_end":100,"line_start":6,"line_end":6,"column_start":5,"column_end":14,"is_primary":true,"text":[{"text":"use core::mem;","highlight_start":5,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"logger/rust_logger/src/generated/logger_generated.rs","byte_start":87,"byte_end":102,"line_start":6,"line_end":7,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use core::mem;","highlight_start":1,"highlight_end":15},{"text":"use core::cmp::Ordering;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `core::mem`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mlogger/rust_logger/src/generated/logger_generated.rs:6:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse core::mem;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `core::cmp::Ordering`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"logger/rust_logger/src/generated/logger_generated.rs","byte_start":106,"byte_end":125,"line_start":7,"line_end":7,"column_start":5,"column_end":24,"is_primary":true,"text":[{"text":"use core::cmp::Ordering;","highlight_start":5,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"logger/rust_logger/src/generated/logger_generated.rs","byte_start":102,"byte_end":127,"line_start":7,"line_end":8,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use core::cmp::Ordering;","highlight_start":1,"highlight_end":25},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `core::cmp::Ordering`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mlogger/rust_logger/src/generated/logger_generated.rs:7:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse core::cmp::Ordering;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `EndianScalar` and `Follow`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"logger/rust_logger/src/generated/logger_generated.rs","byte_start":178,"byte_end":190,"line_start":10,"line_end":10,"column_start":25,"column_end":37,"is_primary":true,"text":[{"text":"use self::flatbuffers::{EndianScalar, Follow};","highlight_start":25,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"logger/rust_logger/src/generated/logger_generated.rs","byte_start":192,"byte_end":198,"line_start":10,"line_end":10,"column_start":39,"column_end":45,"is_primary":true,"text":[{"text":"use self::flatbuffers::{EndianScalar, Follow};","highlight_start":39,"highlight_end":45}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"logger/rust_logger/src/generated/logger_generated.rs","byte_start":154,"byte_end":201,"line_start":10,"line_end":11,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use self::flatbuffers::{EndianScalar, Follow};","highlight_start":1,"highlight_end":47},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `EndianScalar` and `Follow`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mlogger/rust_logger/src/generated/logger_generated.rs:10:25\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse self::flatbuffers::{EndianScalar, Follow};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"constant `STORAGE_KEY_LOG_CONFIG` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"logger/rust_logger/src/storage.rs","byte_start":842,"byte_end":864,"line_start":41,"line_end":41,"column_start":7,"column_end":29,"is_primary":true,"text":[{"text":"const STORAGE_KEY_LOG_CONFIG: &str = \"logger_config\";","highlight_start":7,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(dead_code)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: constant `STORAGE_KEY_LOG_CONFIG` is never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mlogger/rust_logger/src/storage.rs:41:7\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m41\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mconst STORAGE_KEY_LOG_CONFIG: &str = \"logger_config\";\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m       \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(dead_code)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `appender_open_legacy` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"logger/rust_logger/src/xlog_ffi.rs","byte_start":3761,"byte_end":3781,"line_start":117,"line_end":117,"column_start":19,"column_end":39,"is_primary":true,"text":[{"text":"    pub unsafe fn appender_open_legacy(","highlight_start":19,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `appender_open_legacy` is never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mlogger/rust_logger/src/xlog_ffi.rs:117:19\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m117\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub unsafe fn appender_open_legacy(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                   \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `appender_set_max_alive_duration` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"logger/rust_logger/src/xlog_ffi.rs","byte_start":4586,"byte_end":4617,"line_start":146,"line_end":146,"column_start":19,"column_end":50,"is_primary":true,"text":[{"text":"    pub unsafe fn appender_set_max_alive_duration(_max_time: i64) {","highlight_start":19,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `appender_set_max_alive_duration` is never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mlogger/rust_logger/src/xlog_ffi.rs:146:19\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m146\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub unsafe fn appender_set_max_alive_duration(_max_time: i64) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                   \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `xlogger_SetLevel` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"logger/rust_logger/src/xlog_ffi.rs","byte_start":3065,"byte_end":3081,"line_start":97,"line_end":97,"column_start":19,"column_end":35,"is_primary":true,"text":[{"text":"    pub unsafe fn xlogger_SetLevel(_level: XLogLevel) {","highlight_start":19,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(non_snake_case)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"logger/rust_logger/src/xlog_ffi.rs","byte_start":3065,"byte_end":3081,"line_start":97,"line_end":97,"column_start":19,"column_end":35,"is_primary":true,"text":[{"text":"    pub unsafe fn xlogger_SetLevel(_level: XLogLevel) {","highlight_start":19,"highlight_end":35}],"label":null,"suggested_replacement":"xlogger_set_level","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `xlogger_SetLevel` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mlogger/rust_logger/src/xlog_ffi.rs:97:19\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m97\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub unsafe fn xlogger_SetLevel(_level: XLogLevel) {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                   \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `xlogger_set_level`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(non_snake_case)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `xlogger_IsEnabledFor` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"logger/rust_logger/src/xlog_ffi.rs","byte_start":3253,"byte_end":3273,"line_start":103,"line_end":103,"column_start":19,"column_end":39,"is_primary":true,"text":[{"text":"    pub unsafe fn xlogger_IsEnabledFor(_level: XLogLevel) -> c_int {","highlight_start":19,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"logger/rust_logger/src/xlog_ffi.rs","byte_start":3253,"byte_end":3273,"line_start":103,"line_end":103,"column_start":19,"column_end":39,"is_primary":true,"text":[{"text":"    pub unsafe fn xlogger_IsEnabledFor(_level: XLogLevel) -> c_int {","highlight_start":19,"highlight_end":39}],"label":null,"suggested_replacement":"xlogger_is_enabled_for","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `xlogger_IsEnabledFor` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mlogger/rust_logger/src/xlog_ffi.rs:103:19\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m103\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub unsafe fn xlogger_IsEnabledFor(_level: XLogLevel) -> c_int {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                   \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `xlogger_is_enabled_for`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `xlogger_Write` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"logger/rust_logger/src/xlog_ffi.rs","byte_start":3365,"byte_end":3378,"line_start":107,"line_end":107,"column_start":19,"column_end":32,"is_primary":true,"text":[{"text":"    pub unsafe fn xlogger_Write(info: *const XLoggerInfo, log: *const c_char) {","highlight_start":19,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"logger/rust_logger/src/xlog_ffi.rs","byte_start":3365,"byte_end":3378,"line_start":107,"line_end":107,"column_start":19,"column_end":32,"is_primary":true,"text":[{"text":"    pub unsafe fn xlogger_Write(info: *const XLoggerInfo, log: *const c_char) {","highlight_start":19,"highlight_end":32}],"label":null,"suggested_replacement":"xlogger_write","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `xlogger_Write` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mlogger/rust_logger/src/xlog_ffi.rs:107:19\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m107\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub unsafe fn xlogger_Write(info: *const XLoggerInfo, log: *const c_char) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                   \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case (notice the capitalization): `xlogger_write`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"9 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: 9 warnings emitted\u001b[0m\n\n"}
