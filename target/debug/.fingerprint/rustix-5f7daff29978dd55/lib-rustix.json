{"rustc": 15497389221046826682, "features": "[\"alloc\", \"default\", \"fs\", \"std\"]", "declared_features": "[\"all-apis\", \"alloc\", \"compiler_builtins\", \"core\", \"default\", \"event\", \"fs\", \"io_uring\", \"libc\", \"libc_errno\", \"linux_4_11\", \"linux_5_1\", \"linux_5_11\", \"linux_latest\", \"mm\", \"mount\", \"net\", \"param\", \"pipe\", \"process\", \"pty\", \"rand\", \"runtime\", \"rustc-dep-of-std\", \"rustc-std-workspace-alloc\", \"shm\", \"std\", \"stdio\", \"system\", \"termios\", \"thread\", \"time\", \"try_close\", \"use-explicitly-provided-auxv\", \"use-libc\", \"use-libc-auxv\"]", "target": 16221545317719767766, "profile": 2210360036522109082, "path": 7554643852353655800, "deps": [[7896293946984509699, "bitflags", false, 16797539179995640380], [9559541369283268958, "libc", false, 15986481088268281552], [12053020504183902936, "build_script_build", false, 7598831761530243194], [14633813869673313769, "libc_errno", false, 17616023683395981998]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rustix-5f7daff29978dd55/dep-lib-rustix", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}