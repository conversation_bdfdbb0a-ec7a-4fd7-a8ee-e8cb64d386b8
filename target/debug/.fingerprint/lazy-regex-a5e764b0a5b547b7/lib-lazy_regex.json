{"rustc": 15497389221046826682, "features": "[\"default\", \"regex\"]", "declared_features": "[\"default\", \"lite\", \"perf\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"regex\", \"regex-lite\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "target": 373025063261422169, "profile": 5347358027863023418, "path": 3146093022995437134, "deps": [[5070769681332304831, "once_cell", false, 15305827305686893015], [9451456094439810778, "regex", false, 17870400058511013548], [15278928094109957391, "lazy_regex_proc_macros", false, 2689887597349899771]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/lazy-regex-a5e764b0a5b547b7/dep-lib-lazy_regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}