{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"nightly_derive\"]", "target": 18170435632028880183, "profile": 3033921117576893, "path": 1066403825143860612, "deps": [[2828590642173593838, "cfg_if", false, 1880661156984210890], [3060637413840920116, "proc_macro2", false, 7608282993562232869], [9001202093189684693, "build_script_build", false, 4796464914200008973], [10640660562325816595, "syn", false, 9852450757901358922], [17990358020177143287, "quote", false, 6760751990977140778]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/mockall_derive-ae5abdd3ace438cb/dep-lib-mockall_derive", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}