{"rustc": 15497389221046826682, "features": "[\"__tls\", \"blocking\", \"charset\", \"default\", \"default-tls\", \"h2\", \"http2\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\"]", "declared_features": "[\"__internal_proxy_sys_no_cache\", \"__rustls\", \"__rustls-ring\", \"__tls\", \"blocking\", \"brotli\", \"charset\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"gzip\", \"h2\", \"hickory-dns\", \"http2\", \"http3\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-manual-roots-no-provider\", \"rustls-tls-native-roots\", \"rustls-tls-native-roots-no-provider\", \"rustls-tls-no-provider\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"socks\", \"stream\", \"trust-dns\", \"zstd\"]", "target": 8885864859914201979, "profile": 2383083043656166682, "path": 14436355454594184297, "deps": [[40386456601120721, "percent_encoding", false, 694368755636144809], [95042085696191081, "ipnet", false, 10781134783591082933], [385810070298638530, "log", false, 9570944178305552605], [411067296443658118, "serde", false, 17239015535286776521], [418947936956741439, "h2", false, 15068263007139485454], [784494742817713399, "tower_service", false, 6106481685242371478], [985115344064483054, "system_configuration", false, 9642794769261373848], [1791586607557829099, "serde_json", false, 843635556270049232], [1811549171721445101, "futures_channel", false, 2426859889939017999], [1906322745568073236, "pin_project_lite", false, 14947904571586367982], [2011830238986063773, "tokio", false, 8244751758269923175], [2517136641825875337, "sync_wrapper", false, 5034922803081768142], [3150220818285335163, "url", false, 1238372576536554090], [4920660634395069245, "hyper_util", false, 14180106394417198952], [5070769681332304831, "once_cell", false, 15305827305686893015], [5695049318159433696, "tower", false, 6289343486927401715], [7620660491849607393, "futures_core", false, 9777933496889008138], [9010263965687315507, "http", false, 12367237350783474617], [10229185211513642314, "mime", false, 4796850441252442527], [10629569228670356391, "futures_util", false, 12856696984162600777], [11957360342995674422, "hyper", false, 14740775002471841130], [12186126227181294540, "tokio_native_tls", false, 16707552741127415937], [13077212702700853852, "base64", false, 135091148158988038], [14084095096285906100, "http_body", false, 6860670365774824899], [14564311161534545801, "encoding_rs", false, 11584479583216104946], [15032952994102373905, "rustls_pemfile", false, 4271143001902576171], [16066129441945555748, "bytes", false, 16943133799195680342], [16542808166767769916, "serde_urlencoded", false, 18217235115689833546], [16785601910559813697, "native_tls_crate", false, 6631685365403363587], [16900715236047033623, "http_body_util", false, 9905269266290648772], [18071510856783138481, "mime_guess", false, 3361073659417672892], [18273243456331255970, "hyper_tls", false, 479574598061647641]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/reqwest-2ae0f0829463aa1a/dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}