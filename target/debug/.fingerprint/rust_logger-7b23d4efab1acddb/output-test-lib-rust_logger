{"$message_type":"diagnostic","message":"unused import: `core::mem`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"logger/rust_logger/src/generated/logger_generated.rs","byte_start":91,"byte_end":100,"line_start":6,"line_end":6,"column_start":5,"column_end":14,"is_primary":true,"text":[{"text":"use core::mem;","highlight_start":5,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"logger/rust_logger/src/generated/logger_generated.rs","byte_start":87,"byte_end":102,"line_start":6,"line_end":7,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use core::mem;","highlight_start":1,"highlight_end":15},{"text":"use core::cmp::Ordering;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `core::mem`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mlogger/rust_logger/src/generated/logger_generated.rs:6:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse core::mem;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `core::cmp::Ordering`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"logger/rust_logger/src/generated/logger_generated.rs","byte_start":106,"byte_end":125,"line_start":7,"line_end":7,"column_start":5,"column_end":24,"is_primary":true,"text":[{"text":"use core::cmp::Ordering;","highlight_start":5,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"logger/rust_logger/src/generated/logger_generated.rs","byte_start":102,"byte_end":127,"line_start":7,"line_end":8,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use core::cmp::Ordering;","highlight_start":1,"highlight_end":25},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `core::cmp::Ordering`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mlogger/rust_logger/src/generated/logger_generated.rs:7:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse core::cmp::Ordering;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `EndianScalar` and `Follow`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"logger/rust_logger/src/generated/logger_generated.rs","byte_start":178,"byte_end":190,"line_start":10,"line_end":10,"column_start":25,"column_end":37,"is_primary":true,"text":[{"text":"use self::flatbuffers::{EndianScalar, Follow};","highlight_start":25,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"logger/rust_logger/src/generated/logger_generated.rs","byte_start":192,"byte_end":198,"line_start":10,"line_end":10,"column_start":39,"column_end":45,"is_primary":true,"text":[{"text":"use self::flatbuffers::{EndianScalar, Follow};","highlight_start":39,"highlight_end":45}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"logger/rust_logger/src/generated/logger_generated.rs","byte_start":154,"byte_end":201,"line_start":10,"line_end":11,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use self::flatbuffers::{EndianScalar, Follow};","highlight_start":1,"highlight_end":47},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `EndianScalar` and `Follow`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mlogger/rust_logger/src/generated/logger_generated.rs:10:25\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse self::flatbuffers::{EndianScalar, Follow};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `super::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"logger/rust_logger/src/test_mmap.rs","byte_start":66,"byte_end":74,"line_start":5,"line_end":5,"column_start":9,"column_end":17,"is_primary":true,"text":[{"text":"    use super::*;","highlight_start":9,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"logger/rust_logger/src/test_mmap.rs","byte_start":62,"byte_end":75,"line_start":5,"line_end":5,"column_start":5,"column_end":18,"is_primary":true,"text":[{"text":"    use super::*;","highlight_start":5,"highlight_end":18}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `super::*`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mlogger/rust_logger/src/test_mmap.rs:5:9\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m5\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    use super::*;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"constant `STORAGE_KEY_LOG_CONFIG` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"logger/rust_logger/src/storage.rs","byte_start":873,"byte_end":895,"line_start":41,"line_end":41,"column_start":7,"column_end":29,"is_primary":true,"text":[{"text":"const STORAGE_KEY_LOG_CONFIG: &str = \"logger_config\";","highlight_start":7,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(dead_code)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: constant `STORAGE_KEY_LOG_CONFIG` is never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mlogger/rust_logger/src/storage.rs:41:7\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m41\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mconst STORAGE_KEY_LOG_CONFIG: &str = \"logger_config\";\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m       \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(dead_code)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"field `mmap_file` is never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"logger/rust_logger/src/mmap_storage.rs","byte_start":861,"byte_end":875,"line_start":29,"line_end":29,"column_start":12,"column_end":26,"is_primary":false,"text":[{"text":"pub struct MmapLogStorage {","highlight_start":12,"highlight_end":26}],"label":"field in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"logger/rust_logger/src/mmap_storage.rs","byte_start":1026,"byte_end":1035,"line_start":35,"line_end":35,"column_start":5,"column_end":14,"is_primary":true,"text":[{"text":"    mmap_file: File,","highlight_start":5,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: field `mmap_file` is never read\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mlogger/rust_logger/src/mmap_storage.rs:35:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m29\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct MmapLogStorage {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfield in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m35\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    mmap_file: File,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `appender_open_with_cache` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"logger/rust_logger/src/xlog_ffi.rs","byte_start":4331,"byte_end":4355,"line_start":134,"line_end":134,"column_start":19,"column_end":43,"is_primary":true,"text":[{"text":"    pub unsafe fn appender_open_with_cache(","highlight_start":19,"highlight_end":43}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `appender_open_with_cache` is never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mlogger/rust_logger/src/xlog_ffi.rs:134:19\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m134\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub unsafe fn appender_open_with_cache(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                   \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `appender_set_max_alive_duration` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"logger/rust_logger/src/xlog_ffi.rs","byte_start":5091,"byte_end":5122,"line_start":161,"line_end":161,"column_start":19,"column_end":50,"is_primary":true,"text":[{"text":"    pub unsafe fn appender_set_max_alive_duration(_max_time: i64) {","highlight_start":19,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `appender_set_max_alive_duration` is never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mlogger/rust_logger/src/xlog_ffi.rs:161:19\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m161\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub unsafe fn appender_set_max_alive_duration(_max_time: i64) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                   \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `xlogger_SetLevel` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"logger/rust_logger/src/xlog_ffi.rs","byte_start":3396,"byte_end":3412,"line_start":105,"line_end":105,"column_start":19,"column_end":35,"is_primary":true,"text":[{"text":"    pub unsafe fn xlogger_SetLevel(_level: XLogLevel) {","highlight_start":19,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(non_snake_case)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"logger/rust_logger/src/xlog_ffi.rs","byte_start":3396,"byte_end":3412,"line_start":105,"line_end":105,"column_start":19,"column_end":35,"is_primary":true,"text":[{"text":"    pub unsafe fn xlogger_SetLevel(_level: XLogLevel) {","highlight_start":19,"highlight_end":35}],"label":null,"suggested_replacement":"xlogger_set_level","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `xlogger_SetLevel` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mlogger/rust_logger/src/xlog_ffi.rs:105:19\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m105\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub unsafe fn xlogger_SetLevel(_level: XLogLevel) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                   \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `xlogger_set_level`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(non_snake_case)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `xlogger_IsEnabledFor` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"logger/rust_logger/src/xlog_ffi.rs","byte_start":3584,"byte_end":3604,"line_start":111,"line_end":111,"column_start":19,"column_end":39,"is_primary":true,"text":[{"text":"    pub unsafe fn xlogger_IsEnabledFor(_level: XLogLevel) -> c_int {","highlight_start":19,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"logger/rust_logger/src/xlog_ffi.rs","byte_start":3584,"byte_end":3604,"line_start":111,"line_end":111,"column_start":19,"column_end":39,"is_primary":true,"text":[{"text":"    pub unsafe fn xlogger_IsEnabledFor(_level: XLogLevel) -> c_int {","highlight_start":19,"highlight_end":39}],"label":null,"suggested_replacement":"xlogger_is_enabled_for","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `xlogger_IsEnabledFor` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mlogger/rust_logger/src/xlog_ffi.rs:111:19\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m111\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub unsafe fn xlogger_IsEnabledFor(_level: XLogLevel) -> c_int {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                   \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `xlogger_is_enabled_for`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `xlogger_Write` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"logger/rust_logger/src/xlog_ffi.rs","byte_start":3696,"byte_end":3709,"line_start":115,"line_end":115,"column_start":19,"column_end":32,"is_primary":true,"text":[{"text":"    pub unsafe fn xlogger_Write(info: *const XLoggerInfo, log: *const c_char) {","highlight_start":19,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"logger/rust_logger/src/xlog_ffi.rs","byte_start":3696,"byte_end":3709,"line_start":115,"line_end":115,"column_start":19,"column_end":32,"is_primary":true,"text":[{"text":"    pub unsafe fn xlogger_Write(info: *const XLoggerInfo, log: *const c_char) {","highlight_start":19,"highlight_end":32}],"label":null,"suggested_replacement":"xlogger_write","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `xlogger_Write` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mlogger/rust_logger/src/xlog_ffi.rs:115:19\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m115\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub unsafe fn xlogger_Write(info: *const XLoggerInfo, log: *const c_char) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                   \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case (notice the capitalization): `xlogger_write`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"11 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: 11 warnings emitted\u001b[0m\n\n"}
