match key { "af" => Some (AF) , "am" => Some (AM) , "an" => Some (AN) , "ar" => Some (AR) , "ast" => Some (AST) , "az" => Some (AZ) , "bg" => Some (BG) , "bm" => Some (BM) , "bs" => Some (BS) , "ca" => Some (CA) , "cs" => Some (CS) , "cy-GB" => Some (CY_GB) , "da" => Some (DA) , "de" => Some (DE) , "el" => Some (EL) , "em" => Some (EM) , "en" => Some (EN) , "en-Scouse" => Some (EN_SCOUSE) , "en-au" => Some (EN_AU) , "en-lol" => Some (EN_LOL) , "en-old" => Some (EN_OLD) , "en-pirate" => Some (EN_PIRATE) , "en-tx" => Some (EN_TX) , "eo" => Some (EO) , "es" => Some (ES) , "et" => Some (ET) , "fa" => Some (FA) , "fi" => Some (FI) , "fr" => Some (FR) , "ga" => Some (GA) , "gj" => Some (GJ) , "gl" => Some (GL) , "he" => Some (HE) , "hi" => Some (HI) , "hr" => Some (HR) , "ht" => Some (HT) , "hu" => Some (HU) , "id" => Some (ID) , "is" => Some (IS) , "it" => Some (IT) , "ja" => Some (JA) , "jv" => Some (JV) , "ka" => Some (KA) , "kn" => Some (KN) , "ko" => Some (KO) , "lt" => Some (LT) , "lu" => Some (LU) , "lv" => Some (LV) , "mk-Cyrl" => Some (MK_CYRL) , "mk-Latn" => Some (MK_LATN) , "mn" => Some (MN) , "mr" => Some (MR) , "ne" => Some (NE) , "nl" => Some (NL) , "no" => Some (NO) , "pa" => Some (PA) , "pl" => Some (PL) , "pt" => Some (PT) , "ro" => Some (RO) , "ru" => Some (RU) , "sk" => Some (SK) , "sl" => Some (SL) , "sr-Cyrl" => Some (SR_CYRL) , "sr-Latn" => Some (SR_LATN) , "sv" => Some (SV) , "ta" => Some (TA) , "te" => Some (TE) , "th" => Some (TH) , "tlh" => Some (TLH) , "tr" => Some (TR) , "tt" => Some (TT) , "uk" => Some (UK) , "ur" => Some (UR) , "uz" => Some (UZ) , "vi" => Some (VI) , "zh-CN" => Some (ZH_CN) , "zh-TW" => Some (ZH_TW) , _ => None }