//
//  RustLogger.swift
//  iosApp
//
//  Created by Logger Team on 2024/12/10.
//

import Foundation
import FlatBuffers
import RustUplus

public let RustLoggerModuleName = "RustLogger"

@objc public class RustLogger: NSObject {

    // 大文件回调代理
    @objc public static weak var delegate: RustLoggerDelegate?

    /// 初始化日志器
    @objc public static func initialize(config: RustLoggerConfig) -> RustLoggerResult {
        RustLoggerLog.initRustLog()
        
        let configDict: [String: Any] = [
            "action": "init",
            "config": config.toJsonString()
        ]
        
        let loggerResult = getRustLoggerResult(from: configDict)
        return loggerResult
    }
    
    /// 写入日志
    /// 脱敏处理由Rust层统一处理，不需要sensitive参数
    @objc public static func writeLog(level: RustLogLevel, tag: String, message: String, args: [String] = []) -> RustLoggerResult {
        let logEntry: [String: Any] = [
            "level": level.rawValue,
            "tag": tag,
            "format": message,
            "args": args,
            "timestamp": Int64(Date().timeIntervalSince1970 * 1000)
        ]

        let jsonDict: [String: Any] = [
            "action": "write_log",
            "log_entry": logEntry
        ]

        let loggerResult = getRustLoggerResult(from: jsonDict)
        return loggerResult
    }
    
    /// 更新用户ID
    @objc public static func updateUserId(_ userId: String) -> RustLoggerResult {
        let jsonDict: [String: Any] = [
            "action": "update_user_id",
            "user_id": userId
        ]
        
        let loggerResult = getRustLoggerResult(from: jsonDict)
        return loggerResult
    }
    
    /// 手动上传日志
    @objc public static func uploadLogsManually() -> RustLoggerResult {
        let jsonDict: [String: Any] = [
            "action": "upload_logs_manually"
        ]
        
        let loggerResult = getRustLoggerResult(from: jsonDict)
        return loggerResult
    }
    
    /// 写入崩溃日志
    @objc public static func writeCrashLog(_ crashInfo: String) -> RustLoggerResult {
        let jsonDict: [String: Any] = [
            "action": "write_crash_log",
            "crash_info": crashInfo
        ]
        
        let loggerResult = getRustLoggerResult(from: jsonDict)
        return loggerResult
    }
    
    /// 检查是否已初始化
    @objc public static func isInitialized() -> RustLoggerResult {
        let jsonDict: [String: Any] = [
            "action": "is_initialized"
        ]

        let loggerResult = getRustLoggerResult(from: jsonDict)
        return loggerResult
    }

    /// 设置大文件通知回调
    /// 当日志文件超过设置的大小限制时会触发此回调
    @objc public static func setDelegate(_ delegate: RustLoggerDelegate?) {
        self.delegate = delegate
        print("RustLogger: Large file delegate set: \(delegate != nil)")
    }

    /// 压缩日志文件供上传
    /// @param days 压缩最近几天的日志
    /// @return 压缩文件路径
    @objc public static func compressLogsForUpload(days: Int32 = 7) -> RustLoggerResult {
        let jsonDict: [String: Any] = [
            "action": "compress_logs_for_upload",
            "days": days
        ]

        let loggerResult = getRustLoggerResult(from: jsonDict)
        return loggerResult
    }

    /// 压缩崩溃日志供上传
    /// @return 压缩文件路径
    @objc public static func compressCrashLogs() -> RustLoggerResult {
        let jsonDict: [String: Any] = [
            "action": "compress_crash_logs"
        ]

        let loggerResult = getRustLoggerResult(from: jsonDict)
        return loggerResult
    }

    /// 上传成功后清理文件
    /// @param zipPath 压缩文件路径
    @objc public static func cleanupAfterUploadSuccess(zipPath: String) -> RustLoggerResult {
        let jsonDict: [String: Any] = [
            "action": "cleanup_after_upload_success",
            "zip_path": zipPath
        ]

        let loggerResult = getRustLoggerResult(from: jsonDict)
        return loggerResult
    }

    /// 上传失败后清理文件
    /// @param zipPath 压缩文件路径
    @objc public static func cleanupAfterUploadFailure(zipPath: String) -> RustLoggerResult {
        let jsonDict: [String: Any] = [
            "action": "cleanup_after_upload_failure",
            "zip_path": zipPath
        ]

        let loggerResult = getRustLoggerResult(from: jsonDict)
        return loggerResult
    }

    /// 内部方法：处理大文件通知
    /// 由Rust层调用（通过C回调）
    @objc public static func onLargeFileDetected(filePath: String) {
        print("RustLogger: Large file detected: \(filePath)")
        delegate?.onLargeFileDetected(filePath: filePath)
    }
}

// MARK: - Helper Functions

func get_logger_from_flat_data(data: IosReturnBuffer) -> com_haier_uhome_uplus_rust_logger_fbs_LoggerFlat {
    let bytes = Array(UnsafeBufferPointer(start: data.buffer, count: Int(data.length)))
    var bb = ByteBuffer(bytes: bytes)
    let option = VerifierOptions.init(maxDepth: 10000)
    let loggerFlat: com_haier_uhome_uplus_rust_logger_fbs_LoggerFlat = try! getCheckedRoot(byteBuffer: &bb, options: option)
    defer {
        ios_drop_buffer(data)
    }
    return loggerFlat
}

func getRustLoggerResult(from jsonDict: [String: Any]) -> RustLoggerResult {
    guard let jsonData = try? JSONSerialization.data(withJSONObject: jsonDict, options: []),
          let jsonString = String(data: jsonData, encoding: .utf8) else {
        print("Error: Failed to create JSON string")
        return RustLoggerResult(success: false, code: -1, message: "JSON serialization failed")
    }

    guard let cString = jsonString.cString(using: .utf8) else {
        print("Error: Failed to convert JSON string to C string")
        return RustLoggerResult(success: false, code: -1, message: "String conversion failed")
    }

    let result = ios_flat_cross_platform("lib_logger", cString)
    let loggerResult = convertIosReturnBufferToRustLoggerResult(buffer: result)
    return loggerResult
}

func convertIosReturnBufferToRustLoggerResult(buffer: IosReturnBuffer) -> RustLoggerResult {
    let loggerFlat = get_logger_from_flat_data(data: buffer)
    let result = RustLoggerResult(success: loggerFlat.success, code: loggerFlat.code, message: loggerFlat.message ?? "")

    let containerType = loggerFlat.containerType
    switch containerType {
    case .boolwrapper:
        if let boolWrapper: com_haier_uhome_uplus_rust_logger_fbs_BoolWrapper = loggerFlat.container(type: com_haier_uhome_uplus_rust_logger_fbs_BoolWrapper.self) {
            result.data = boolWrapper.value
        }
    case .strwrapper:
        if let strWrapper: com_haier_uhome_uplus_rust_logger_fbs_StrWrapper = loggerFlat.container(type: com_haier_uhome_uplus_rust_logger_fbs_StrWrapper.self) {
            result.data = strWrapper.value
        }
    case .int32wrapper:
        if let int32Wrapper: com_haier_uhome_uplus_rust_logger_fbs_Int32Wrapper = loggerFlat.container(type: com_haier_uhome_uplus_rust_logger_fbs_Int32Wrapper.self) {
            result.data = int32Wrapper.value
        }
    default:
        result.data = loggerFlat.success
    }

    return result
}
