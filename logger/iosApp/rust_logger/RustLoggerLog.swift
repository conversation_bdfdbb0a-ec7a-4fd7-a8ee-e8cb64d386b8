//
//  RustLoggerLog.swift
//  iosApp
//
//  Created by Logger Team on 2024/12/10.
//

import Foundation
import uplog

// 定义日志级别枚举
enum RustLoggerLogLevel: Int32 {
    case error = 1
    case warning = 2
    case info = 3
    case debug = 4
    case trace = 0
}

fileprivate func rust_logger_logger(tag: UnsafePointer<Int8>?,
                                    content: UnsafePointer<Int8>?,
                                    level: Int32) {
    let logLevel = RustLoggerLogLevel(rawValue: level) ?? .trace
    RustLoggerLog.printRustLog(
        tag: String(cString: tag!),
        content: String(cString: content!),
        level: logLevel
    )
}

class RustLoggerLog {
    static func initRustLog() {
        // 注意：这里需要根据实际的Rust接口来调用
        // setup_logger_logger(rust_logger_logger)
        print("RustLoggerLog initialized")
    }

    fileprivate static func printRustLog(tag: String, content: String, level: RustLoggerLogLevel) {
        let message = "[\(tag)]\(content)"
        switch level.rawValue {
        case 1:
            error(message)
        case 2:
            warning(message)
        case 3:
            info(message)
        case 4:
            debug(message)
        default:
            trace(message)
        }
    }
    
    private static func debug(_ message: String) {
        UPPrintDebug(moduleName: RustLoggerModuleName, message: message)
    }
    
    private static func warning(_ message: String) {
        UPPrintWarning(moduleName: RustLoggerModuleName, message: message)
    }
    
    private static func info(_ message: String) {
        UPPrintInfo(moduleName: RustLoggerModuleName, message: message)
    }
    
    private static func error(_ message: String) {
        UPPrintError(moduleName: RustLoggerModuleName, message: message)
    }
    
    private static func trace(_ message: String) {
        UPPrintVerbose(moduleName: RustLoggerModuleName, message: message)
    }
    
    private init(){}
}
