//
//  RustLoggerResult.swift
//  iosApp
//
//  Created by Logger Team on 2024/12/10.
//

import Foundation

@objcMembers public class RustLoggerResult: NSObject {
    
    enum Code: Int32 {
        case success = 0
        case argumentError = -1
        case jniEnvError = -2
        case jniReturnNull = -3
        case initializationError = -4
        case notInitialized = -5
    }
    
    /// Export success code to OC because inner enum of swift class is unavailable to OC
    @objc public static let SuccessCode: Int32 = RustLoggerResult.Code.success.rawValue
    @objc public static let ArgumentErrorCode: Int32 = RustLoggerResult.Code.argumentError.rawValue
    @objc public static let NotInitializedCode: Int32 = RustLoggerResult.Code.notInitialized.rawValue
    
    public var success: Bool = false
    public var code: Int32 = RustLoggerResult.SuccessCode
    public var message: String = ""
    
    /// 返回的数据，类型可能是 Bool/String/Int 之一
    public var data: Any?
    
    public init(success: Bool, code: Int32, message: String) {
        self.success = success
        self.code = code
        self.message = message
        super.init()
    }
    
    /// 是否成功
    public var isSuccess: Bool {
        return code == RustLoggerResult.SuccessCode
    }
    
    /// 是否失败
    public var isFailure: Bool {
        return code != RustLoggerResult.SuccessCode
    }
}
