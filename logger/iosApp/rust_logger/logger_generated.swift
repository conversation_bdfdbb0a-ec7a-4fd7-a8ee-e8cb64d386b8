// automatically generated by the FlatBuffers compiler, do not modify
// swiftlint:disable all
// swiftformat:disable all

import FlatBuffers

public enum com_haier_uhome_uplus_rust_logger_fbs_LoggerContainer: UInt8, UnionEnum {
  public typealias T = UInt8

  public init?(value: T) {
    self.init(rawValue: value)
  }

  public static var byteSize: Int { return MemoryLayout<UInt8>.size }
  public var value: UInt8 { return self.rawValue }
  case none_ = 0
  case boolwrapper = 1
  case strwrapper = 2
  case int32wrapper = 3
  case logentry = 4
  case loggerconfig = 5
  case uploadprogress = 6
  case logstats = 7

  public static var max: com_haier_uhome_uplus_rust_logger_fbs_LoggerContainer { return .logstats }
  public static var min: com_haier_uhome_uplus_rust_logger_fbs_LoggerContainer { return .none_ }
}

public struct com_haier_uhome_uplus_rust_logger_fbs_BoolWrapper: FlatBufferObject, Verifiable {

  static func validateVersion() { FlatBuffersVersion_24_3_25() }
  public var __buffer: ByteBuffer! { return _accessor.bb }
  private var _accessor: Table

  private init(_ t: Table) { _accessor = t }
  public init(_ bb: ByteBuffer, o: Int32) { _accessor = Table(bb: bb, position: o) }

  private enum VTOFFSET: VOffset {
    case value = 4
    var v: Int32 { Int32(self.rawValue) }
    var p: VOffset { self.rawValue }
  }

  public var value: Bool { let o = _accessor.offset(VTOFFSET.value.v); return o == 0 ? false : _accessor.readBuffer(of: Bool.self, at: o) }
  public static func startBoolWrapper(_ fbb: inout FlatBufferBuilder) -> UOffset { fbb.startTable(with: 1) }
  public static func add(value: Bool, _ fbb: inout FlatBufferBuilder) { fbb.add(element: value, def: false, at: VTOFFSET.value.p) }
  public static func endBoolWrapper(_ fbb: inout FlatBufferBuilder, start: UOffset) -> Offset { let end = Offset(offset: fbb.endTable(at: start)); return end }

  public static func verify<T>(_ verifier: inout Verifier, at position: Int, of type: T.Type) throws where T: Verifiable {
    var _v = try verifier.visitTable(at: position)
    try _v.visit(field: VTOFFSET.value.p, fieldName: "value", required: false, type: Bool.self)
    _v.finish()
  }
}

public struct com_haier_uhome_uplus_rust_logger_fbs_StrWrapper: FlatBufferObject, Verifiable {

  static func validateVersion() { FlatBuffersVersion_24_3_25() }
  public var __buffer: ByteBuffer! { return _accessor.bb }
  private var _accessor: Table

  private init(_ t: Table) { _accessor = t }
  public init(_ bb: ByteBuffer, o: Int32) { _accessor = Table(bb: bb, position: o) }

  private enum VTOFFSET: VOffset {
    case value = 4
    var v: Int32 { Int32(self.rawValue) }
    var p: VOffset { self.rawValue }
  }

  public var value: String? { let o = _accessor.offset(VTOFFSET.value.v); return o == 0 ? nil : _accessor.string(at: o) }
  public static func startStrWrapper(_ fbb: inout FlatBufferBuilder) -> UOffset { fbb.startTable(with: 1) }
  public static func add(value: Offset, _ fbb: inout FlatBufferBuilder) { fbb.add(offset: value, at: VTOFFSET.value.p) }
  public static func endStrWrapper(_ fbb: inout FlatBufferBuilder, start: UOffset) -> Offset { let end = Offset(offset: fbb.endTable(at: start)); return end }

  public static func verify<T>(_ verifier: inout Verifier, at position: Int, of type: T.Type) throws where T: Verifiable {
    var _v = try verifier.visitTable(at: position)
    try _v.visit(field: VTOFFSET.value.p, fieldName: "value", required: false, type: ForwardOffset<String>.self)
    _v.finish()
  }
}

public struct com_haier_uhome_uplus_rust_logger_fbs_Int32Wrapper: FlatBufferObject, Verifiable {

  static func validateVersion() { FlatBuffersVersion_24_3_25() }
  public var __buffer: ByteBuffer! { return _accessor.bb }
  private var _accessor: Table

  private init(_ t: Table) { _accessor = t }
  public init(_ bb: ByteBuffer, o: Int32) { _accessor = Table(bb: bb, position: o) }

  private enum VTOFFSET: VOffset {
    case value = 4
    var v: Int32 { Int32(self.rawValue) }
    var p: VOffset { self.rawValue }
  }

  public var value: Int32 { let o = _accessor.offset(VTOFFSET.value.v); return o == 0 ? 0 : _accessor.readBuffer(of: Int32.self, at: o) }
  public static func startInt32Wrapper(_ fbb: inout FlatBufferBuilder) -> UOffset { fbb.startTable(with: 1) }
  public static func add(value: Int32, _ fbb: inout FlatBufferBuilder) { fbb.add(element: value, def: 0, at: VTOFFSET.value.p) }
  public static func endInt32Wrapper(_ fbb: inout FlatBufferBuilder, start: UOffset) -> Offset { let end = Offset(offset: fbb.endTable(at: start)); return end }

  public static func verify<T>(_ verifier: inout Verifier, at position: Int, of type: T.Type) throws where T: Verifiable {
    var _v = try verifier.visitTable(at: position)
    try _v.visit(field: VTOFFSET.value.p, fieldName: "value", required: false, type: Int32.self)
    _v.finish()
  }
}

public struct com_haier_uhome_uplus_rust_logger_fbs_LoggerFlat: FlatBufferObject, Verifiable {

  static func validateVersion() { FlatBuffersVersion_24_3_25() }
  public var __buffer: ByteBuffer! { return _accessor.bb }
  private var _accessor: Table

  private init(_ t: Table) { _accessor = t }
  public init(_ bb: ByteBuffer, o: Int32) { _accessor = Table(bb: bb, position: o) }

  private enum VTOFFSET: VOffset {
    case containerType = 4
    case container = 6
    case code = 8
    case message = 10
    case success = 12
    var v: Int32 { Int32(self.rawValue) }
    var p: VOffset { self.rawValue }
  }

  public var containerType: com_haier_uhome_uplus_rust_logger_fbs_LoggerContainer { let o = _accessor.offset(VTOFFSET.containerType.v); return o == 0 ? .none_ : com_haier_uhome_uplus_rust_logger_fbs_LoggerContainer(rawValue: _accessor.readBuffer(of: UInt8.self, at: o)) ?? .none_ }
  public func container<T: FlatbuffersInitializable>(type: T.Type) -> T? { let o = _accessor.offset(VTOFFSET.container.v); return o == 0 ? nil : _accessor.union(o) }
  public var code: Int32 { let o = _accessor.offset(VTOFFSET.code.v); return o == 0 ? 0 : _accessor.readBuffer(of: Int32.self, at: o) }
  public var message: String? { let o = _accessor.offset(VTOFFSET.message.v); return o == 0 ? nil : _accessor.string(at: o) }
  public var success: Bool { let o = _accessor.offset(VTOFFSET.success.v); return o == 0 ? false : _accessor.readBuffer(of: Bool.self, at: o) }

  public static func startLoggerFlat(_ fbb: inout FlatBufferBuilder) -> UOffset { fbb.startTable(with: 5) }
  public static func add(containerType: com_haier_uhome_uplus_rust_logger_fbs_LoggerContainer, _ fbb: inout FlatBufferBuilder) { fbb.add(element: containerType.rawValue, def: 0, at: VTOFFSET.containerType.p) }
  public static func add(container: Offset, _ fbb: inout FlatBufferBuilder) { fbb.add(offset: container, at: VTOFFSET.container.p) }
  public static func add(code: Int32, _ fbb: inout FlatBufferBuilder) { fbb.add(element: code, def: 0, at: VTOFFSET.code.p) }
  public static func add(message: Offset, _ fbb: inout FlatBufferBuilder) { fbb.add(offset: message, at: VTOFFSET.message.p) }
  public static func add(success: Bool, _ fbb: inout FlatBufferBuilder) { fbb.add(element: success, def: false, at: VTOFFSET.success.p) }
  public static func endLoggerFlat(_ fbb: inout FlatBufferBuilder, start: UOffset) -> Offset { let end = Offset(offset: fbb.endTable(at: start)); return end }

  public static func verify<T>(_ verifier: inout Verifier, at position: Int, of type: T.Type) throws where T: Verifiable {
    var _v = try verifier.visitTable(at: position)
    try _v.visit(unionKey: VTOFFSET.containerType.p, unionField: VTOFFSET.container.p, unionKeyName: "containerType", fieldName: "container", required: false, completion: { (verifier, key: com_haier_uhome_uplus_rust_logger_fbs_LoggerContainer, pos) in
      switch key {
      case .none_:
        break
      case .boolwrapper:
        try ForwardOffset<com_haier_uhome_uplus_rust_logger_fbs_BoolWrapper>.verify(&verifier, at: pos, of: com_haier_uhome_uplus_rust_logger_fbs_BoolWrapper.self)
      case .strwrapper:
        try ForwardOffset<com_haier_uhome_uplus_rust_logger_fbs_StrWrapper>.verify(&verifier, at: pos, of: com_haier_uhome_uplus_rust_logger_fbs_StrWrapper.self)
      case .int32wrapper:
        try ForwardOffset<com_haier_uhome_uplus_rust_logger_fbs_Int32Wrapper>.verify(&verifier, at: pos, of: com_haier_uhome_uplus_rust_logger_fbs_Int32Wrapper.self)
      default:
        break
      }
    })
    try _v.visit(field: VTOFFSET.code.p, fieldName: "code", required: false, type: Int32.self)
    try _v.visit(field: VTOFFSET.message.p, fieldName: "message", required: false, type: ForwardOffset<String>.self)
    try _v.visit(field: VTOFFSET.success.p, fieldName: "success", required: false, type: Bool.self)
    _v.finish()
  }
}
