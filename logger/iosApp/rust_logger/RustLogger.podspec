Pod::Spec.new do |spec|
  spec.name         = "RustLogger"
  spec.version      = "1.0.0"
  spec.summary      = "Rust logger for multiplatform."
  spec.description  = <<-DESC
  A static library built by Rust for unified logging across platforms.
                   DESC

  spec.homepage     = "https://git.haier.net/uplus/shell/cocoapods/rust_logger"
  spec.license      = "MIT"
  spec.author             = { "Logger Team" => "<EMAIL>" }
  spec.platform     = :ios, "12.0"
  spec.swift_version = "5.0"
  spec.source       = { :git => "https://git.haier.net/uplus/shell/cocoapods/rust_logger.git", :tag => "#{spec.version}" }

  spec.source_files  = "**/*.{h,c,cpp,m,mm,swift}"
  spec.libraries = "resolv", "c++", "z"
  spec.vendored_libraries = "libs/libxlog.a"

  spec.pod_target_xcconfig = {
    "DEFINES_MODULE" => "YES"
  }
  
  spec.dependency 'uplog', '>= 1.7.4'
  spec.dependency 'FlatBuffers', '>= 24.3.25'
  spec.dependency 'RustUplus', '>= 0.2.0'

end
