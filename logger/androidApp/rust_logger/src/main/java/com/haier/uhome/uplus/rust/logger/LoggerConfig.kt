package com.haier.uhome.uplus.rust.logger

import org.json.JSONObject

/**
 * 日志器配置类
 */
data class LoggerConfig(
    // 基础配置
    var logLevel: LogLevel = LogLevel.WARN,
    var enableConsoleOutput: Boolean = false,  // 默认关闭控制台输出（对应Android默认值）
    var enableFullLog: Boolean = false,        // 全量日志开关（对应Android的enableFullLogs）
    var testMode: Boolean = false,
    var logEnv: String = "SC", // SC: 生产环境, YS: 验收环境
    var disableSensitiveWords: Boolean = false, // 是否禁用脱敏
    
    // 用户信息
    var userId: String = "0",
    var deviceId: String = "",
    var sessionId: String = "",
    var appVersion: String = "",
    
    // 隐私配置
    var privacyAgreed: Boolean = false,
    var isDebugMode: Boolean = false,
    
    // 文件配置
    var maxFileSize: Long = 20 * 1024 * 1024, // 20MB
    var maxDirectorySize: Long = 600 * 1024 * 1024, // 600MB
    var logFilePrefix: String = "uplog",
    var logDirectory: String = "", // 必须设置

    // 格式化配置
    var customPrefix: String = "",
    
    // 性能配置
    var maxLogLength: Int = 4000,
    var maxLogsPerSecond: Int = 2000,
    var bufferSize: Int = 8192,
    
    // Android对齐的关键配置
    var versionName: String = "" // 版本名称，用作一级目录
) {
    
    /**
     * 转换为JSON字符串
     */
    fun toJson(): String {
        val json = JSONObject()
        
        // 基础配置
        json.put("log_level", logLevel.value)
        json.put("enable_console_output", enableConsoleOutput)
        json.put("enable_full_log", enableFullLog)
        json.put("test_mode", testMode)
        json.put("log_env", logEnv)
        json.put("disable_sensitive_words", disableSensitiveWords)
        
        // 用户信息
        json.put("user_id", userId)
        json.put("device_id", deviceId)
        json.put("session_id", sessionId)
        json.put("app_version", appVersion)
        
        // 隐私配置
        json.put("privacy_agreed", privacyAgreed)
        json.put("is_debug_mode", isDebugMode)
        
        // 文件配置
        json.put("max_file_size", maxFileSize)
        json.put("max_directory_size", maxDirectorySize)
        json.put("log_file_prefix", logFilePrefix)
        json.put("log_directory", logDirectory)

        // 格式化配置
        json.put("custom_prefix", customPrefix)
        
        // 性能配置
        json.put("max_log_length", maxLogLength)
        json.put("max_logs_per_second", maxLogsPerSecond)
        json.put("buffer_size", bufferSize)
        
        // Android对齐的关键配置
        json.put("version_name", versionName)
        
        return json.toString()
    }
}
