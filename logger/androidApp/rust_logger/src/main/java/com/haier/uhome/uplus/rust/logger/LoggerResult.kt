package com.haier.uhome.uplus.rust.logger

/**
 * Logger操作结果类
 */
data class LoggerResult<T>(
    val data: T,
    val code: Int = 0,
    val message: String = ""
) {
    companion object {
        const val SUCCESS = 0
        const val ARGUMENT_ERROR = -1
        const val JNI_ENV_ERROR = -2
        const val JNI_RETURN_NULL = -3
        const val INITIALIZATION_ERROR = -4
        const val NOT_INITIALIZED = -5
        
        const val ERROR_NAME_NULL = "Name cannot be null or empty"
        const val ERROR_NOT_INITIALIZED = "Logger not initialized"
    }
    
    /**
     * 是否成功
     */
    val isSuccess: Boolean
        get() = code == SUCCESS
    
    /**
     * 是否失败
     */
    val isFailure: Boolean
        get() = code != SUCCESS
}
