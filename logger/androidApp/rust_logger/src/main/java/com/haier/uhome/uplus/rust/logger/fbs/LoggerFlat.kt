// automatically generated by the FlatBuffers compiler, do not modify

package com.haier.uhome.uplus.rust.logger.fbs

import com.google.flatbuffers.BaseVector
import com.google.flatbuffers.BooleanVector
import com.google.flatbuffers.ByteVector
import com.google.flatbuffers.Constants
import com.google.flatbuffers.DoubleVector
import com.google.flatbuffers.FlatBufferBuilder
import com.google.flatbuffers.FloatVector
import com.google.flatbuffers.LongVector
import com.google.flatbuffers.StringVector
import com.google.flatbuffers.Struct
import com.google.flatbuffers.Table
import com.google.flatbuffers.UnionVector
import java.nio.ByteBuffer
import java.nio.ByteOrder
import kotlin.jvm.JvmStatic

@Suppress("unused")
class LoggerFlat : Table() {

    fun __init(_i: Int, _bb: ByteBuffer): LoggerFlat {
        return __assign(_i, _bb)
    }

    fun __assign(_i: Int, _bb: ByteBuffer): LoggerFlat {
        __reset(_i, _bb)
        return this
    }

    val containerType: Byte
        get() {
            val o = __offset(4)
            return if (o != 0) bb.get(o + bb_pos) else 0
        }

    fun container(obj: Table): Table? {
        val o = __offset(6)
        return if (o != 0) __union(obj, o + bb_pos) else null
    }

    val code: Int
        get() {
            val o = __offset(8)
            return if (o != 0) bb.getInt(o + bb_pos) else 0
        }

    val message: String?
        get() {
            val o = __offset(10)
            return if (o != 0) __string(o + bb_pos) else null
        }

    val success: Boolean
        get() {
            val o = __offset(12)
            return if (o != 0) 0.toByte() != bb.get(o + bb_pos) else false
        }

    companion object {
        @JvmStatic
        fun validateVersion() = Constants.FLATBUFFERS_24_3_25()

        @JvmStatic
        fun getRootAsLoggerFlat(_bb: ByteBuffer): LoggerFlat = getRootAsLoggerFlat(_bb, LoggerFlat())

        @JvmStatic
        fun getRootAsLoggerFlat(_bb: ByteBuffer, obj: LoggerFlat): LoggerFlat {
            _bb.order(ByteOrder.LITTLE_ENDIAN)
            return obj.__assign(_bb.getInt(_bb.position()) + _bb.position(), _bb)
        }

        @JvmStatic
        fun startLoggerFlat(builder: FlatBufferBuilder) = builder.startTable(5)

        @JvmStatic
        fun addContainerType(builder: FlatBufferBuilder, containerType: Byte) = builder.addByte(0, containerType, 0)

        @JvmStatic
        fun addContainer(builder: FlatBufferBuilder, container: Int) = builder.addOffset(1, container, 0)

        @JvmStatic
        fun addCode(builder: FlatBufferBuilder, code: Int) = builder.addInt(2, code, 0)

        @JvmStatic
        fun addMessage(builder: FlatBufferBuilder, message: Int) = builder.addOffset(3, message, 0)

        @JvmStatic
        fun addSuccess(builder: FlatBufferBuilder, success: Boolean) = builder.addBoolean(4, success, false)

        @JvmStatic
        fun endLoggerFlat(builder: FlatBufferBuilder): Int = builder.endTable()
    }
}
