// automatically generated by the FlatBuffers compiler, do not modify

package com.haier.uhome.uplus.rust.logger.fbs

@Suppress("unused")
object LoggerContainer {
    const val NONE: Byte = 0
    const val BoolWrapper: Byte = 1
    const val StrWrapper: Byte = 2
    const val Int32Wrapper: Byte = 3
    const val LogEntry: Byte = 4
    const val LoggerConfig: Byte = 5
    const val UploadProgress: Byte = 6
    const val LogStats: Byte = 7
}
