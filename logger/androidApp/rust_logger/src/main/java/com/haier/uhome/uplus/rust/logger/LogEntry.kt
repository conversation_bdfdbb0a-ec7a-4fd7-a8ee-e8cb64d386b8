package com.haier.uhome.uplus.rust.logger

import org.json.JSONArray
import org.json.JSONObject

/**
 * 日志条目类
 * 脱敏处理由Rust层统一处理，不需要sensitive参数
 */
data class LogEntry(
    val level: LogLevel,
    val tag: String,
    val message: String,
    val args: List<String> = emptyList()
) {

    /**
     * 转换为JSON字符串
     */
    fun toJson(): String {
        val json = JSONObject()

        json.put("level", level.value)
        json.put("tag", tag)
        json.put("format", message)

        // 参数数组
        val argsArray = JSONArray()
        args.forEach { argsArray.put(it) }
        json.put("args", argsArray)

        return json.toString()
    }
}
