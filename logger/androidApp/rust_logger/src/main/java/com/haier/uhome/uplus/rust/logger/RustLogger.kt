package com.haier.uhome.uplus.rust.logger

import android.content.Context
import android.util.Log
import com.haier.uhome.uplus.rust.channels.RustChannel
import com.haier.uhome.uplus.rust.logger.fbs.BoolWrapper
import com.haier.uhome.uplus.rust.logger.fbs.LoggerContainer
import com.haier.uhome.uplus.rust.logger.fbs.LoggerFlat
import com.haier.uhome.uplus.rust.logger.fbs.StrWrapper
import java.nio.ByteBuffer

/**
 * Rust Logger Android Bridge
 * 
 * 基于RustChannel的统一日志功能，支持：
 * - 多级别日志输出
 * - 文件存储和管理
 * - 隐私信息脱敏
 * - 日志上传功能
 * - 三端统一配置
 */
object RustLogger {
    private const val TAG = "RustLogger"
    private const val LIB_NAME = "lib_logger"

    // Action常量定义
    private const val INIT_ACTION = "init"
    private const val WRITE_LOG_ACTION = "write_log"
    private const val UPDATE_USER_ID_ACTION = "update_user_id"
    private const val UPLOAD_LOGS_MANUALLY_ACTION = "upload_logs_manually"
    private const val WRITE_CRASH_LOG_ACTION = "write_crash_log"
    private const val IS_INITIALIZED_ACTION = "is_initialized"
    private const val COMPRESS_LOGS_ACTION = "compress_logs_for_upload"
    private const val COMPRESS_CRASH_LOGS_ACTION = "compress_crash_logs"
    private const val CLEANUP_SUCCESS_ACTION = "cleanup_after_upload_success"
    private const val CLEANUP_FAILURE_ACTION = "cleanup_after_upload_failure"

    // 大文件回调
    private var largeFileCallback: LargeFileCallback? = null

    /**
     * 初始化日志器
     */
    fun initialize(context: Context, config: LoggerConfig): LoggerResult<Boolean> {
        var bys: ByteBuffer? = null
        return try {
            if (config.logDirectory.isEmpty()) {
                config.logDirectory = "${context.filesDir}/logs"
            }
            
            val params = mutableMapOf("config" to config.toJson())
            bys = getRustBuffer(INIT_ACTION, params)
            parseLoggerResult(bys) ?: LoggerResult(false, -1, "JNI return null")
        } catch (e: Exception) {
            Log.e(TAG, "Exception during initialization", e)
            LoggerResult(false, -2, e.message ?: "Unknown error")
        } finally {
            bys?.let { RustChannel.releaseBuffer(it) }
        }
    }

    /**
     * 写入日志
     * 脱敏处理由Rust层统一处理，不需要外部传入sensitive参数
     */
    fun writeLog(
        level: LogLevel,
        tag: String,
        message: String,
        args: Array<String> = emptyArray()
    ): LoggerResult<Boolean> {
        var bys: ByteBuffer? = null
        return try {
            val logEntry = LogEntry(level, tag, message, args.toList())
            val params = mutableMapOf("log_entry" to logEntry.toJson())
            bys = getRustBuffer(WRITE_LOG_ACTION, params)
            parseLoggerResult(bys) ?: LoggerResult(false, -1, "JNI return null")
        } catch (e: Exception) {
            Log.e(TAG, "Exception during log writing", e)
            LoggerResult(false, -2, e.message ?: "Unknown error")
        } finally {
            bys?.let { RustChannel.releaseBuffer(it) }
        }
    }

    /**
     * 更新用户ID
     */
    fun updateUserId(userId: String): LoggerResult<Boolean> {
        var bys: ByteBuffer? = null
        return try {
            val params = mutableMapOf("user_id" to userId)
            bys = getRustBuffer(UPDATE_USER_ID_ACTION, params)
            parseLoggerResult(bys) ?: LoggerResult(false, -1, "JNI return null")
        } catch (e: Exception) {
            Log.e(TAG, "Exception during updateUserId", e)
            LoggerResult(false, -2, e.message ?: "Unknown error")
        } finally {
            bys?.let { RustChannel.releaseBuffer(it) }
        }
    }
    
    /**
     * 手动上传日志
     */
    fun uploadLogsManually(): LoggerResult<Boolean> {
        var bys: ByteBuffer? = null
        return try {
            val params = mutableMapOf<String, String>()
            bys = getRustBuffer(UPLOAD_LOGS_MANUALLY_ACTION, params)
            parseLoggerResult(bys) ?: LoggerResult(false, -1, "JNI return null")
        } catch (e: Exception) {
            Log.e(TAG, "Exception during uploadLogsManually", e)
            LoggerResult(false, -2, e.message ?: "Unknown error")
        } finally {
            bys?.let { RustChannel.releaseBuffer(it) }
        }
    }

    /**
     * 写入崩溃日志
     */
    fun writeCrashLog(crashInfo: String): LoggerResult<Boolean> {
        var bys: ByteBuffer? = null
        return try {
            val params = mutableMapOf("crash_info" to crashInfo)
            bys = getRustBuffer(WRITE_CRASH_LOG_ACTION, params)
            parseLoggerResult(bys) ?: LoggerResult(false, -1, "JNI return null")
        } catch (e: Exception) {
            Log.e(TAG, "Exception during writeCrashLog", e)
            LoggerResult(false, -2, e.message ?: "Unknown error")
        } finally {
            bys?.let { RustChannel.releaseBuffer(it) }
        }
    }

    /**
     * 设置大文件通知回调
     * 当日志文件超过设置的大小限制时会触发此回调
     */
    fun setLargeFileCallback(callback: LargeFileCallback?) {
        largeFileCallback = callback
        Log.d(TAG, "Large file callback set: ${callback != null}")
    }

    /**
     * 压缩日志文件供上传
     * @param days 压缩最近几天的日志
     * @return 压缩文件路径
     */
    fun compressLogsForUpload(days: Int = 7): LoggerResult<String> {
        var bys: ByteBuffer? = null
        return try {
            val params = mutableMapOf("days" to days.toString())
            bys = getRustBuffer(COMPRESS_LOGS_ACTION, params)
            parseLoggerResult(bys) ?: LoggerResult("", -1, "JNI return null")
        } catch (e: Exception) {
            Log.e(TAG, "Exception during compressLogsForUpload", e)
            LoggerResult("", -2, e.message ?: "Unknown error")
        } finally {
            bys?.let { RustChannel.releaseBuffer(it) }
        }
    }

    /**
     * 压缩崩溃日志供上传
     * @return 压缩文件路径
     */
    fun compressCrashLogs(): LoggerResult<String> {
        var bys: ByteBuffer? = null
        return try {
            val params = mutableMapOf<String, String>()
            bys = getRustBuffer(COMPRESS_CRASH_LOGS_ACTION, params)
            parseLoggerResult(bys) ?: LoggerResult("", -1, "JNI return null")
        } catch (e: Exception) {
            Log.e(TAG, "Exception during compressCrashLogs", e)
            LoggerResult("", -2, e.message ?: "Unknown error")
        } finally {
            bys?.let { RustChannel.releaseBuffer(it) }
        }
    }

    /**
     * 上传成功后清理文件
     * @param zipPath 压缩文件路径
     */
    fun cleanupAfterUploadSuccess(zipPath: String): LoggerResult<Boolean> {
        var bys: ByteBuffer? = null
        return try {
            val params = mutableMapOf("zip_path" to zipPath)
            bys = getRustBuffer(CLEANUP_SUCCESS_ACTION, params)
            parseLoggerResult(bys) ?: LoggerResult(false, -1, "JNI return null")
        } catch (e: Exception) {
            Log.e(TAG, "Exception during cleanupAfterUploadSuccess", e)
            LoggerResult(false, -2, e.message ?: "Unknown error")
        } finally {
            bys?.let { RustChannel.releaseBuffer(it) }
        }
    }

    /**
     * 上传失败后清理文件
     * @param zipPath 压缩文件路径
     */
    fun cleanupAfterUploadFailure(zipPath: String): LoggerResult<Boolean> {
        var bys: ByteBuffer? = null
        return try {
            val params = mutableMapOf("zip_path" to zipPath)
            bys = getRustBuffer(CLEANUP_FAILURE_ACTION, params)
            parseLoggerResult(bys) ?: LoggerResult(false, -1, "JNI return null")
        } catch (e: Exception) {
            Log.e(TAG, "Exception during cleanupAfterUploadFailure", e)
            LoggerResult(false, -2, e.message ?: "Unknown error")
        } finally {
            bys?.let { RustChannel.releaseBuffer(it) }
        }
    }

    /**
     * 内部方法：处理大文件通知
     * 由Rust层调用（通过JNI回调）
     */
    @JvmStatic
    fun onLargeFileDetected(filePath: String) {
        Log.d(TAG, "Large file detected: $filePath")
        largeFileCallback?.onLargeFileDetected(filePath)
    }

    /**
     * 调用Rust方法
     */
    private fun getRustBuffer(action: String, params: Map<String, String>): ByteBuffer? {
        return RustChannel.callRustMethod(LIB_NAME, action, params)
    }
    
    /**
     * 解析Logger结果 - 使用FlatBuffers
     */
    private inline fun <reified T> parseLoggerResult(buffer: ByteBuffer?): LoggerResult<T>? {
        return parseLoggerResult<T>(buffer)
    }
}
