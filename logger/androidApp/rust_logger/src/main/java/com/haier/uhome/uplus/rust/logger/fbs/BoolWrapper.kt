// automatically generated by the FlatBuffers compiler, do not modify

package com.haier.uhome.uplus.rust.logger.fbs

import com.google.flatbuffers.BaseVector
import com.google.flatbuffers.BooleanVector
import com.google.flatbuffers.ByteVector
import com.google.flatbuffers.Constants
import com.google.flatbuffers.DoubleVector
import com.google.flatbuffers.FlatBufferBuilder
import com.google.flatbuffers.FloatVector
import com.google.flatbuffers.LongVector
import com.google.flatbuffers.StringVector
import com.google.flatbuffers.Struct
import com.google.flatbuffers.Table
import com.google.flatbuffers.UnionVector
import java.nio.ByteBuffer
import java.nio.ByteOrder
import kotlin.jvm.JvmStatic

@Suppress("unused")
class BoolWrapper : Table() {

    fun __init(_i: Int, _bb: ByteBuffer): BoolWrapper {
        return __assign(_i, _bb)
    }

    fun __assign(_i: Int, _bb: ByteBuffer): <PERSON><PERSON><PERSON>rapper {
        __reset(_i, _bb)
        return this
    }

    val value: Boolean
        get() {
            val o = __offset(4)
            return if (o != 0) 0.toByte() != bb.get(o + bb_pos) else false
        }

    companion object {
        @JvmStatic
        fun validateVersion() = Constants.FLATBUFFERS_24_3_25()

        @JvmStatic
        fun getRootAsBoolWrapper(_bb: ByteBuffer): BoolWrapper = getRootAsBoolWrapper(_bb, BoolWrapper())

        @JvmStatic
        fun getRootAsBoolWrapper(_bb: ByteBuffer, obj: BoolWrapper): BoolWrapper {
            _bb.order(ByteOrder.LITTLE_ENDIAN)
            return obj.__assign(_bb.getInt(_bb.position()) + _bb.position(), _bb)
        }

        @JvmStatic
        fun startBoolWrapper(builder: FlatBufferBuilder) = builder.startTable(1)

        @JvmStatic
        fun addValue(builder: FlatBufferBuilder, value: Boolean) = builder.addBoolean(0, value, false)

        @JvmStatic
        fun endBoolWrapper(builder: FlatBufferBuilder): Int = builder.endTable()
    }
}
