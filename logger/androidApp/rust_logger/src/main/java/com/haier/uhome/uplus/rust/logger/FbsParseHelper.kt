package com.haier.uhome.uplus.rust.logger

import com.haier.uhome.uplus.rust.logger.fbs.BoolWrapper
import com.haier.uhome.uplus.rust.logger.fbs.LoggerContainer
import com.haier.uhome.uplus.rust.logger.fbs.LoggerFlat
import com.haier.uhome.uplus.rust.logger.fbs.StrWrapper
import java.nio.ByteBuffer

/**
 * FlatBuffers解析辅助函数
 */
fun <T> parseLoggerResult(bysBuffer: ByteBuffer?): LoggerResult<T>? {
    if (bysBuffer == null) {
        return null
    }
    
    return try {
        val loggerResult = LoggerFlat.getRootAsLoggerFlat(bysBuffer)
        
        @Suppress("UNCHECKED_CAST")
        val data = when (loggerResult.containerType) {
            LoggerContainer.BoolWrapper -> {
                val value = (loggerResult.container(BoolWrapper()) as <PERSON><PERSON><PERSON>rapper?)?.value ?: false
                value as T
            }
            LoggerContainer.StrWrapper -> {
                val value = (loggerResult.container(StrWrapper()) as StrWrapper?)?.value ?: ""
                value as T
            }
            else -> {
                // 默认返回success状态
                loggerResult.success as T
            }
        }
        
        LoggerResult(
            data = data,
            code = loggerResult.code,
            message = loggerResult.message ?: ""
        )
    } catch (e: Exception) {
        android.util.Log.e("FbsParseHelper", "Failed to parse logger result", e)
        null
    }
}
