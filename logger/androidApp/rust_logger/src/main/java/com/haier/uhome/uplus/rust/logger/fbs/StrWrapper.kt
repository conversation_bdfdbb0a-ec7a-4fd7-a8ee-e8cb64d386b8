// automatically generated by the FlatBuffers compiler, do not modify

package com.haier.uhome.uplus.rust.logger.fbs

import com.google.flatbuffers.BaseVector
import com.google.flatbuffers.BooleanVector
import com.google.flatbuffers.ByteVector
import com.google.flatbuffers.Constants
import com.google.flatbuffers.DoubleVector
import com.google.flatbuffers.FlatBufferBuilder
import com.google.flatbuffers.FloatVector
import com.google.flatbuffers.LongVector
import com.google.flatbuffers.StringVector
import com.google.flatbuffers.Struct
import com.google.flatbuffers.Table
import com.google.flatbuffers.UnionVector
import java.nio.ByteBuffer
import java.nio.ByteOrder
import kotlin.jvm.JvmStatic

@Suppress("unused")
class StrWrapper : Table() {

    fun __init(_i: Int, _bb: ByteBuffer): StrWrapper {
        return __assign(_i, _bb)
    }

    fun __assign(_i: Int, _bb: ByteBuffer): StrWrapper {
        __reset(_i, _bb)
        return this
    }

    val value: String?
        get() {
            val o = __offset(4)
            return if (o != 0) __string(o + bb_pos) else null
        }

    companion object {
        @JvmStatic
        fun validateVersion() = Constants.FLATBUFFERS_24_3_25()

        @JvmStatic
        fun getRootAsStrWrapper(_bb: ByteBuffer): StrWrapper = getRootAsStrWrapper(_bb, StrWrapper())

        @JvmStatic
        fun getRootAsStrWrapper(_bb: ByteBuffer, obj: StrWrapper): StrWrapper {
            _bb.order(ByteOrder.LITTLE_ENDIAN)
            return obj.__assign(_bb.getInt(_bb.position()) + _bb.position(), _bb)
        }

        @JvmStatic
        fun startStrWrapper(builder: FlatBufferBuilder) = builder.startTable(1)

        @JvmStatic
        fun addValue(builder: FlatBufferBuilder, value: Int) = builder.addOffset(0, value, 0)

        @JvmStatic
        fun endStrWrapper(builder: FlatBufferBuilder): Int = builder.endTable()
    }
}
