// automatically generated by the FlatBuffers compiler, do not modify

package com.haier.uhome.uplus.rust.logger.fbs

import com.google.flatbuffers.BaseVector
import com.google.flatbuffers.BooleanVector
import com.google.flatbuffers.ByteVector
import com.google.flatbuffers.Constants
import com.google.flatbuffers.DoubleVector
import com.google.flatbuffers.FlatBufferBuilder
import com.google.flatbuffers.FloatVector
import com.google.flatbuffers.LongVector
import com.google.flatbuffers.StringVector
import com.google.flatbuffers.Struct
import com.google.flatbuffers.Table
import com.google.flatbuffers.UnionVector
import java.nio.ByteBuffer
import java.nio.ByteOrder
import kotlin.jvm.JvmStatic

@Suppress("unused")
class Int32Wrapper : Table() {

    fun __init(_i: Int, _bb: ByteBuffer): Int32Wrapper {
        return __assign(_i, _bb)
    }

    fun __assign(_i: Int, _bb: ByteBuffer): Int32Wrapper {
        __reset(_i, _bb)
        return this
    }

    val value: Int
        get() {
            val o = __offset(4)
            return if (o != 0) bb.getInt(o + bb_pos) else 0
        }

    companion object {
        @JvmStatic
        fun validateVersion() = Constants.FLATBUFFERS_24_3_25()

        @JvmStatic
        fun getRootAsInt32Wrapper(_bb: ByteBuffer): Int32Wrapper = getRootAsInt32Wrapper(_bb, Int32Wrapper())

        @JvmStatic
        fun getRootAsInt32Wrapper(_bb: ByteBuffer, obj: Int32Wrapper): Int32Wrapper {
            _bb.order(ByteOrder.LITTLE_ENDIAN)
            return obj.__assign(_bb.getInt(_bb.position()) + _bb.position(), _bb)
        }

        @JvmStatic
        fun startInt32Wrapper(builder: FlatBufferBuilder) = builder.startTable(1)

        @JvmStatic
        fun addValue(builder: FlatBufferBuilder, value: Int) = builder.addInt(0, value, 0)

        @JvmStatic
        fun endInt32Wrapper(builder: FlatBufferBuilder): Int = builder.endTable()
    }
}
