package com.haier.uhome.uplus.rust.logger

/**
 * 日志级别枚举
 */
enum class LogLevel(val value: Int, val levelName: String) {
    TRACE(0, "TRACE"),
    DEBUG(1, "DEBUG"),
    INFO(2, "INFO"),
    WARN(3, "WARN"),
    ERROR(4, "ERROR");
    
    companion object {
        /**
         * 从数值获取日志级别
         */
        fun fromValue(value: Int): LogLevel {
            return values().find { it.value == value } ?: WARN
        }
        
        /**
         * 从字符串获取日志级别
         */
        fun fromString(levelName: String): LogLevel {
            return values().find { it.levelName.equals(levelName, ignoreCase = true) } ?: WARN
        }
    }
    
    override fun toString(): String = levelName
}
