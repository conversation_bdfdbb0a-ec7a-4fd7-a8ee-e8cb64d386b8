pluginManagement {
    repositories {
        maven(url = "https://mdpm.haier.net/nexus/repository/public")
        maven(url = "https://mdpm.haier.net/nexus/content/groups/public")

        google {
            content {
                includeGroupByRegex("com\\.android.*")
                includeGroupByRegex("com\\.google.*")
                includeGroupByRegex("androidx.*")
            }
        }
        mavenCentral()
        gradlePluginPortal()
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        maven(url = "https://mdpm.haier.net/nexus/repository/public")
        maven(url = "https://mdpm.haier.net/nexus/content/groups/public")

        google()
        mavenCentral()
    }
}

rootProject.name = "androidApp"
include(":app")
include(":rust_storage")
