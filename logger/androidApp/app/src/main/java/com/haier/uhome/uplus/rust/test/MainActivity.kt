package com.haier.uhome.uplus.rust.test

import android.os.Bundle
import android.os.SystemClock
import android.util.Log
import androidx.appcompat.app.AppCompatActivity
import com.haier.uhome.uplus.rust.storage.OnDataChangeListener
import com.haier.uhome.uplus.rust.storage.RustStorage
import com.haier.uhome.uplus.rust.storage.SortType
import com.haier.uhome.uplus.rust.storage.databinding.ActivityMainBinding
import kotlin.concurrent.thread


class MainActivity : AppCompatActivity() {
    /**
     * A native method that is implemented by the 'storage' native library,
     * which is packaged with this application.
     */
//    private external fun stringFromJNI(): String

    companion object {
        private const val TAG = "MainActivity"

        // Used to load the 'storage' library on application startup.
        init {
            System.loadLibrary("rust_uplus")
        }
    }

    private val binding by viewBinding<ActivityMainBinding>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val dbHelper = DatabaseHelper(this);
        dbHelper.checkSQLiteVersion()

        val logFile = getExternalFilesDir("log")
        Log.d(TAG, "onCreate: $logFile")
        val dbPath = getDatabasePath("ignore").parent
        Log.d(TAG, "onCreate: $dbPath")
        if (dbPath != null) {
            RustStorage.init(dbPath)
        }
//        Log.d(TAG, "onCreate: ${stringFromJNI()}")
        val listener = NamedDataChangeListener("a/b/c")
        RustStorage.addNodeChangedListener("a", NamedDataChangeListener("a"))
        RustStorage.addNodeChangedListener("a/b", NamedDataChangeListener("a/b"))
        RustStorage.addNodeChangedListener("a/b/c", listener)
        binding.btPut.setOnClickListener {
            multiThread {
                Log.d(TAG, "onCreate: putIntValue --> ${RustStorage.putIntValue("int", 9)}")
                Log.d(TAG, "onCreate: putLongValue --> ${RustStorage.putLongValue("long", 10000L)}")
                Log.d(TAG, "onCreate: putFloatValue --> ${RustStorage.putFloatValue("float", 99.9f)}")
                Log.d(
                    TAG, "onCreate: putDoubleValue --> ${
                        RustStorage.putDoubleValue(
                            "double",
                            666.66
                        )
                    }")
                Log.d(
                    TAG, "onCreate: putBooleanValue --> ${
                        RustStorage.putBooleanValue(
                            "bool",
                            true
                        )
                    }")
                Log.d(
                    TAG, "onCreate: putStringValue --> ${
                        RustStorage.putStringValue(
                            "string",
                            "abc"
                        )
                    }")
                Log.d(
                    TAG, "onCreate: putMemoryString --> ${
                        RustStorage.putMemoryString(
                            "memoryString",
                            "www"
                        )
                    }")
                Log.d(TAG, "onCreate: putIntValue - subNode ${RustStorage.putIntValue("a", 66)}")
                Log.d(TAG, "onCreate: putIntValue - subNode ${RustStorage.putIntValue("a/b", 2)}")
                Log.d(TAG, "onCreate: putIntValue - subNode ${RustStorage.putIntValue("a/b/c", 100)}")
                Log.d(TAG, "onCreate: putIntValue - subNode ${RustStorage.putIntValue("a/b/d", 200)}")
                Log.d(TAG, "onCreate: putIntValue - subNode ${RustStorage.putIntValue("a/b/e", 300)}")
                Log.d(TAG, "onCreate: putIntValue - subNode ${RustStorage.putIntValue("a/m/n", 10)}")
                Log.d(TAG, "onCreate: putIntValue - subNode ${RustStorage.putIntValue("a/m/o", 20)}")
                Log.d(TAG, "onCreate: putIntValue - subNode ${RustStorage.putIntValue("a/m/p", 30)}")
            }
        }
        binding.btGet.setOnClickListener {
            multiThread {
                Log.d(TAG, "onCreate: getIntValue --> ${RustStorage.getIntValue("int", 0)}")
                Log.d(TAG, "onCreate: getLongValue --> ${RustStorage.getLongValue("long", 0L)}")
                Log.d(TAG, "onCreate: getFloatValue --> ${RustStorage.getFloatValue("float", 0f)}")
                Log.d(
                    TAG, "onCreate: getDoubleValue --> ${
                        RustStorage.getDoubleValue(
                            "double",
                            0.0
                        )
                    }")
                Log.d(
                    TAG, "onCreate: getBooleanValue --> ${
                        RustStorage.getBooleanValue(
                            "bool",
                            false
                        )
                    }")
                Log.d(TAG, "onCreate: getStringValue --> ${RustStorage.getStringValue("string", "")}")
                Log.d(
                    TAG, "onCreate: memoryString --> ${
                        RustStorage.getMemoryString(
                            "memoryString",
                            ""
                        )
                    }")
                Log.d(TAG, "onCreate: ${RustStorage.getIntSubNodes("a")}")
                Log.d(TAG, "onCreate: ${RustStorage.getIntSubNodes("a/b", SortType.ASC)}")
                Log.d(TAG, "onCreate: ${RustStorage.getIntSubNodes("a/b", SortType.DESC)}")
                Log.d(TAG, "onCreate: ${RustStorage.getIntSubNodes("a/b/c", 100)}")
                Log.d(TAG, "onCreate: ${RustStorage.getIntSubNodes("a/z")}")
            }
        }
        binding.btDelete.setOnClickListener {
            Log.d(TAG, "onCreate: ${RustStorage.deleteNode("int")}")
            Log.d(TAG, "onCreate: ${RustStorage.deleteNode("a/b/c")}")
        }
        binding.btAddListener.setOnClickListener {

        }
        binding.btRemoveListener.setOnClickListener {
            RustStorage.removeNodeChangedListener("a/b/c", listener)
        }
        binding.btMmap.setOnClickListener {
        }
    }

    private fun multiThread(tcount: Int = 1, interval: Long = 0, block: () -> Unit) {
        thread {
            repeat(tcount) {
                thread {
                    block()
                }
                if (interval > 0) {
                    SystemClock.sleep(interval)
                }
            }
        }
    }
    private class NamedDataChangeListener(private val lisName: String) : OnDataChangeListener {
        override fun onDataChanged(action: String, name: String) {
            Log.d(TAG, "onCreate:<$lisName> onDataChanged: $action, $name")
        }
    }


}