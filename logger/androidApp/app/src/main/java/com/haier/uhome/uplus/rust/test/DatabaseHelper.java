package com.haier.uhome.uplus.rust.test;

import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;
import android.content.Context;
import android.util.Log;

public class DatabaseHelper extends SQLiteOpenHelper {

    private static final String DATABASE_NAME = "example.db";
    private static final int DATABASE_VERSION = 1;

    public DatabaseHelper(Context context) {
        super(context, DATABASE_NAME, null, DATABASE_VERSION);
    }

    @Override
    public void onCreate(SQLiteDatabase db) {
        // 在这里创建数据库表格，例如：
        // db.execSQL("CREATE TABLE my_table (column1 INTEGER, column2 TEXT)");
    }

    @Override
    public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
        // 在这里处理数据库升级
    }

    public void checkSQLiteVersion() {
        SQLiteDatabase db = this.getReadableDatabase();

        Cursor cursor = db.rawQuery("SELECT sqlite_version() AS sqlite_version", null);
        if (cursor.moveToFirst()) {
            String sqliteVersion = cursor.getString(0);
            Log.d("SQLite Version", sqliteVersion);
        }
        cursor.close();
        db.close();
    }
}
