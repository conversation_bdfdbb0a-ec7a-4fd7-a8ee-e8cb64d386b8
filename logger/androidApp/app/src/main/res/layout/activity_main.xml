<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.haier.uhome.uplus.rust.test.MainActivity"
    tools:ignore="HardcodedText">

    <Button
        android:id="@+id/bt_put"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:text="put"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <Button
        android:id="@+id/bt_get"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:text="get"
        app:layout_constraintStart_toEndOf="@+id/bt_put"
        app:layout_constraintTop_toTopOf="@+id/bt_put" />
    <Button
        android:id="@+id/bt_delete"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:text="delete"
        app:layout_constraintStart_toEndOf="@id/bt_get"
        app:layout_constraintTop_toTopOf="@id/bt_get" />

    <Button
        android:id="@+id/bt_mmap"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:text="mmap"
        app:layout_constraintStart_toEndOf="@id/bt_delete"
        app:layout_constraintTop_toTopOf="@id/bt_get" />

    <Button
        android:id="@+id/bt_add_listener"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:text="add_listener"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/bt_get" />
    <Button
        android:id="@+id/bt_remove_listener"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:text="remove_listener"
        app:layout_constraintStart_toEndOf="@id/bt_add_listener"
        app:layout_constraintTop_toBottomOf="@id/bt_get" />

</androidx.constraintlayout.widget.ConstraintLayout>