{"app": {"bundleName": "com.haier.uhome.uplus.rust.storage.app", "versionCode": 1000000, "versionName": "1.0.0", "minAPIVersion": 50000012, "targetAPIVersion": 50000012, "apiReleaseType": "Release", "compileSdkVersion": "5.0.0.71", "compileSdkType": "HarmonyOS", "appEnvironments": [], "bundleType": "app"}, "module": {"name": "rust_storage", "type": "har", "deviceTypes": ["default", "tablet", "2in1"], "packageName": "@uplus/rust_storage", "installationFree": false, "virtualMachine": "ark12.0.2.0", "compileMode": "esmodule", "dependencies": []}}