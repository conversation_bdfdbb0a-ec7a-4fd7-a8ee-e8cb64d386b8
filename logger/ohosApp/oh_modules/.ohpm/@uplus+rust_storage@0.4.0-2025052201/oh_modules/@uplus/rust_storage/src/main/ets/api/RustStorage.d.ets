import { NodeData } from './NodeData';
import { StorageResult } from './StorageResult';
export declare class RustStorage {
    static initRustStorage(z2: Context): void;
    static putInt(w2: string, x2: number): StorageResult<boolean>;
    static putLong(t2: string, u2: number): StorageResult<boolean>;
    static putDouble(q2: string, r2: number): StorageResult<boolean>;
    static putBool(n2: string, o2: boolean): StorageResult<boolean>;
    static putString(k2: string, l2: string): StorageResult<boolean>;
    static getInt(g2: string, h2: number): StorageResult<number>;
    static getLong(c2: string, d2: number): StorageResult<number>;
    static getDouble(y1: string, z1: number): StorageResult<number>;
    static getBool(u1: string, v1: boolean): StorageResult<boolean>;
    static getString(q1: string, r1: string): StorageResult<string>;
    static deleteNode(o1: string): StorageResult<boolean>;
    static getIntSubNodes(l1: string, m1: number): StorageResult<Array<NodeData<number>>>;
    static getLongSubNodes(i1: string, j1: number): StorageResult<Array<NodeData<number>>>;
    static getDoubleSubNodes(f1: string, g1: number): StorageResult<Array<NodeData<number>>>;
    static getBoolSubNodes(d1: string): StorageResult<Array<NodeData<boolean>>>;
    static getStringSubNodes(b1: string): StorageResult<Array<NodeData<string>>>;
    static putMemoryString(y: string, z: string): StorageResult<boolean>;
    static getMemoryString(v: string, w: string): StorageResult<string>;
    static deleteMemoryString(t: string): StorageResult<boolean>;
    static clearMemoryString(): StorageResult<boolean>;
    static addNodeChangedListener(p: string, q: (action: string, p: string) => void, r: string): void;
    static removeNodeChangedListener(m: string, n: (action: string, m: string) => void, o: string): void;
    static addMemoryListener(j: string, k: (action: string, j: string) => void, l: string): void;
    static removeMemoryListener(g: string, h: (action: string, g: string) => void, i: string): void;
}
