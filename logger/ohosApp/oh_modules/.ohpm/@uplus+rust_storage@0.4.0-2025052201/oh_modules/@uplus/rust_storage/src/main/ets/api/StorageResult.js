export class StorageResult {
    constructor(c3, d3, e3) {
        this.code = c3;
        this.error = d3;
        this.value = e3;
    }
    isSuccess() {
        return this.code === ResultCode.Success;
    }
}
export var ResultCode;
(function (b3) {
    b3[b3["Success"] = 0] = "Success";
    b3[b3["ArgumentError"] = 1] = "ArgumentError";
    b3[b3["TypeError"] = 2] = "TypeError";
    b3[b3["UpdateNonLeafError"] = 3] = "UpdateNonLeafError";
    b3[b3["DatabaseQueryError"] = 100] = "DatabaseQueryError";
    b3[b3["DatabaseUpdateError"] = 101] = "DatabaseUpdateError";
    b3[b3["DatabaseInsertError"] = 102] = "DatabaseInsertError";
    b3[b3["DatabaseDeleteError"] = 103] = "DatabaseDeleteError";
    b3[b3["DatabaseBeginTransError"] = 104] = "DatabaseBeginTransError";
    b3[b3["MemoryNodeNotFound"] = 200] = "MemoryNodeNotFound";
})(ResultCode || (ResultCode = {}));
