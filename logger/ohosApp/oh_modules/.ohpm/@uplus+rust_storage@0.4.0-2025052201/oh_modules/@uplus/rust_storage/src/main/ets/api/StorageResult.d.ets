export declare class StorageResult<T> {
    value?: T;
    code: number;
    error: string;
    constructor(c3: number, d3: string, e3?: T);
    isSuccess(): boolean;
}
export declare enum ResultCode {
    Success = 0,
    ArgumentError = 1,
    TypeError = 2,
    UpdateNonLeafError = 3,
    DatabaseQueryError = 100,
    DatabaseUpdateError = 101,
    DatabaseInsertError = 102,
    DatabaseDeleteError = 103,
    DatabaseBeginTransError = 104,
    MemoryNodeNotFound = 200
}
