import rustStorage from 'librust_uplus.so';
import { StorageResult } from './StorageResult';
export class RustStorage {
    static initRustStorage(z2) {
        const a3 = `${z2.databaseDir}/rdb`;
        rustStorage.initStorage(a3);
    }
    static putInt(w2, x2) {
        const y2 = rustStorage.putInt(w2, x2);
        return new StorageResult(y2.code, y2.error, y2.value);
    }
    static putLong(t2, u2) {
        const v2 = rustStorage.putLong(t2, u2);
        return new StorageResult(v2.code, v2.error, v2.value);
    }
    static putDouble(q2, r2) {
        const s2 = rustStorage.putDouble(q2, r2);
        return new StorageResult(s2.code, s2.error, s2.value);
    }
    static putBool(n2, o2) {
        const p2 = rustStorage.putBool(n2, o2);
        return new StorageResult(p2.code, p2.error, p2.value);
    }
    static putString(k2, l2) {
        const m2 = rustStorage.putString(k2, l2);
        return new StorageResult(m2.code, m2.error, m2.value);
    }
    static getInt(g2, h2) {
        const i2 = (typeof h2 === 'number') ? h2 : 0;
        const j2 = rustStorage.getInt(g2, i2);
        return new StorageResult(j2.code, j2.error, j2.value);
    }
    static getLong(c2, d2) {
        const e2 = (typeof d2 === 'number') ? d2 : 0;
        const f2 = rustStorage.getLong(c2, e2);
        return new StorageResult(f2.code, f2.error, f2.value);
    }
    static getDouble(y1, z1) {
        const a2 = (typeof z1 === 'number') ? z1 : 0;
        const b2 = rustStorage.getDouble(y1, a2);
        return new StorageResult(b2.code, b2.error, b2.value);
    }
    static getBool(u1, v1) {
        const w1 = (typeof v1 === 'boolean') ? v1 : false;
        const x1 = rustStorage.getBool(u1, w1);
        return new StorageResult(x1.code, x1.error, x1.value);
    }
    static getString(q1, r1) {
        const s1 = (typeof r1 === 'string') ? r1 : String(r1);
        const t1 = rustStorage.getString(q1, s1);
        return new StorageResult(t1.code, t1.error, t1.value);
    }
    static deleteNode(o1) {
        const p1 = rustStorage.deleteNode(o1);
        return new StorageResult(p1.code, p1.error, p1.value);
    }
    static getIntSubNodes(l1, m1) {
        const n1 = rustStorage.getIntSubNodes(l1, m1);
        return new StorageResult(n1.code, n1.error, n1.value);
    }
    static getLongSubNodes(i1, j1) {
        const k1 = rustStorage.getLongSubNodes(i1, j1);
        return new StorageResult(k1.code, k1.error, k1.value);
    }
    static getDoubleSubNodes(f1, g1) {
        const h1 = rustStorage.getDoubleSubNodes(f1, g1);
        return new StorageResult(h1.code, h1.error, h1.value);
    }
    static getBoolSubNodes(d1) {
        const e1 = rustStorage.getBoolSubNodes(d1);
        return new StorageResult(e1.code, e1.error, e1.value);
    }
    static getStringSubNodes(b1) {
        const c1 = rustStorage.getStringSubNodes(b1);
        return new StorageResult(c1.code, c1.error, c1.value);
    }
    static putMemoryString(y, z) {
        const a1 = rustStorage.putMemoryString(y, z);
        return new StorageResult(a1.code, a1.error, a1.value);
    }
    static getMemoryString(v, w) {
        const x = rustStorage.getMemoryString(v, w);
        return new StorageResult(x.code, x.error, x.value);
    }
    static deleteMemoryString(t) {
        const u = rustStorage.deleteMemoryString(t);
        return new StorageResult(u.code, u.error, u.value);
    }
    static clearMemoryString() {
        const s = rustStorage.clearMemoryString();
        return new StorageResult(s.code, s.error, s.value);
    }
    static addNodeChangedListener(p, q, r) {
        rustStorage.addNodeChangedListener(p, q, r);
    }
    static removeNodeChangedListener(m, n, o) {
        rustStorage.removeNodeChangedListener(m, n, o);
    }
    static addMemoryListener(j, k, l) {
        rustStorage.addMemoryListener(j, k, l);
    }
    static removeMemoryListener(g, h, i) {
        rustStorage.removeMemoryListener(g, h, i);
    }
}
