import "reflect-metadata";
import { FileLogger } from '../log/FileLogger';
import common from "@ohos.app.ability.common";
export declare class RustChannel {
    private static instance;
    fileLogger?: FileLogger;
    private constructor();
    init(context: common.Context, debug: boolean): void;
    /**
     * Rust 同步方法
     */
    getRustBuffer(libName: string, params: Map<string, string>): ArrayBuffer | null;
    /**
     * Rust 异步方法
     */
    getRustBufferAsync(libName: string, params: Map<string, string>, callback: (data: ArrayBuffer) => void): void;
    /**
     * Rust 监听注册
     */
    manageRustBufferListener(libName: string, params: Map<string, string>, callback: (data: ArrayBuffer) => void): void;
    /**
     * Rust 监听注册；带返回数据
     */
    manageRustBufferListenerWithData(libName: string, params: Map<string, string>, callback: (data: ArrayBuffer) => void): ArrayBuffer | null;
    static getInstance(): RustChannel;
}
