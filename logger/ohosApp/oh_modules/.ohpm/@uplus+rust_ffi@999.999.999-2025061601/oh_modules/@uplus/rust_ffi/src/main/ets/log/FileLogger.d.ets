import common from "@ohos.app.ability.common";
export declare class FileLogger {
    private suffix;
    private enable;
    private currentDay;
    private logFile?;
    private context;
    constructor(context: common.Context, suffix: string, enable: boolean);
    logToFile(tag: string, msg: string): void;
    setWriteEnable(enable: boolean): void;
    private logToFileAsync;
    private writeLog;
    private openFile;
    private getCurrentDateFileName;
    private getLogDate;
    private parseError;
}
