{"app": {"bundleName": "com.haier.uhome.uplus.rust.ffi", "debug": false, "versionCode": 1000000, "versionName": "1.0.0", "minAPIVersion": 50000012, "targetAPIVersion": 50005017, "apiReleaseType": "Release", "compileSdkVersion": "5.0.5.165", "compileSdkType": "HarmonyOS", "appEnvironments": [], "bundleType": "app", "buildMode": "release"}, "module": {"name": "rust_ffi", "type": "har", "deviceTypes": ["default", "tablet", "2in1"], "packageName": "@uplus/rust_ffi", "installationFree": false, "virtualMachine": "ark12.0.2.0", "compileMode": "esmodule", "dependencies": []}}