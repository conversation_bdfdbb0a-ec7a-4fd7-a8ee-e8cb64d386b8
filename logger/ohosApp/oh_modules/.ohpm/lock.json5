{"lockVersion": "1.0", "settings": {"resolveConflict": true, "resolveConflictStrict": false, "installAll": true}, "overrides": {}, "overrideDependencyMap": {}, "modules": {".": {"name": "", "dependencies": {}, "devDependencies": {"@ohos/hypium": {"specifier": "1.0.18", "version": "1.0.18"}, "@ohos/hamock": {"specifier": "1.0.0", "version": "1.0.0"}}, "dynamicDependencies": {}, "maskedByOverrideDependencyMap": false}, "entry": {"name": "entry", "dependencies": {"@uplus/rust_logger": {"specifier": "file:rust_logger", "version": "file:rust_logger"}, "@uplus/rust_ffi": {"specifier": "1.1.0-2025061601", "version": "999.999.999-2025061601"}, "@uplus/rust_storage": {"specifier": "0.4.0-2025052201", "version": "0.4.0-2025052201"}}, "devDependencies": {}, "dynamicDependencies": {}, "maskedByOverrideDependencyMap": false}, "rust_logger": {"name": "rust_logger", "dependencies": {"@ohos/flatbuffers": {"specifier": "^1.0.1", "version": "1.0.1"}, "@uplus/rust_ffi": {"specifier": "999.999.999-2025061601", "version": "999.999.999-2025061601"}}, "devDependencies": {}, "dynamicDependencies": {}, "maskedByOverrideDependencyMap": false}}, "packages": {"@ohos/hypium@1.0.18": {"integrity": "sha512-RGe/iLGdeywdQilMWZsHKUoiE9OJ+9QxQsorF92R2ImLNVHVhbpSePNITGpW7TnvLgOIP/jscOqfIOhk6X7XRQ==", "storePath": "oh_modules/.ohpm/@ohos+hypium@1.0.18", "dependencies": {}, "dynamicDependencies": {}, "dev": true, "dynamic": false, "maskedByOverrideDependencyMap": false}, "@ohos/hamock@1.0.0": {"integrity": "sha512-K6lDPYc6VkKe6ZBNQa9aoG+ZZMiwqfcR/7yAVFSUGIuOAhPvCJAo9+t1fZnpe0dBRBPxj2bxPPbKh69VuyAtDg==", "storePath": "oh_modules/.ohpm/@ohos+hamock@1.0.0", "dependencies": {}, "dynamicDependencies": {}, "dev": true, "dynamic": false, "maskedByOverrideDependencyMap": false}, "@ohos/flatbuffers@1.0.1": {"integrity": "sha512-pFmt4zsJEzrHckKrBsScBOob2PzuAJkZTrvWHQZ+sYM0c+Jpdefm4WNQEgVtqfAxPF+7xoIhAskzIv6sXK2z3g==", "storePath": "oh_modules/.ohpm/@ohos+flatbuffers@1.0.1", "dependencies": {}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "@uplus/rust_ffi@999.999.999-2025061601": {"integrity": "sha512-r8cwYCTPeUOxr5h1cD8XUzfJoazcWyU8ku39pXRS9b5s+LhpVPV0dxTQApYHveErvVIeqWTDpnAhYFmAfnuUZg==", "storePath": "oh_modules/.ohpm/@uplus+rust_ffi@999.999.999-2025061601", "dependencies": {"class-transformer": "0.5.1", "reflect-metadata": "0.2.1"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "class-transformer@0.5.1": {"integrity": "sha512-SQa1Ws6hUbfC98vKGxZH3KFY0Y1lm5Zm0SY8XX9zbK7FJCyVEac3ATW0RIpwzW+oOfmHE5PMPufDG9hCfoEOMw==", "storePath": "oh_modules/.ohpm/class-transformer@0.5.1", "dependencies": {}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "reflect-metadata@0.2.1": {"integrity": "sha512-i5lLI6iw9AU3Uu4szRNPPEkomnkjRTaVt9hy/bn5g/oSzekBSMeLZblcjP74AW0vBabqERLLIrz+gR8QYR54Tw==", "storePath": "oh_modules/.ohpm/reflect-metadata@0.2.1", "dependencies": {}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "@uplus/rust_logger@file:rust_logger": {"storePath": "rust_logger", "dependencies": {"@ohos/flatbuffers": "1.0.1", "@uplus/rust_ffi": "999.999.999-2025061601"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "@uplus/rust_storage@0.4.0-2025052201": {"integrity": "sha512-PaUSh/AZQLgMLwcqF2AtZ03wnj/tf80PT4C7sC8W+P6tv4YSml25tdoI4xMBVnMo1oc7xxwVC/lStZnVqgUWtg==", "storePath": "oh_modules/.ohpm/@uplus+rust_storage@0.4.0-2025052201", "dependencies": {}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}}}