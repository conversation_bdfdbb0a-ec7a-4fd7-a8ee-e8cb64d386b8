{"version": 3, "file": "transform-instance-to-instance.decorator.js", "sourceRoot": "", "sources": ["../../../src/decorators/transform-instance-to-instance.decorator.ts"], "names": [], "mappings": ";;;AAAA,0DAAuD;AAGvD;;;;GAIG;AACH,SAAgB,2BAA2B,CAAC,MAA8B;IACxE,OAAO,UAAU,MAA2B,EAAE,WAA4B,EAAE,UAA8B;QACxG,MAAM,gBAAgB,GAAqB,IAAI,mCAAgB,EAAE,CAAC;QAClE,MAAM,cAAc,GAAG,UAAU,CAAC,KAAK,CAAC;QAExC,UAAU,CAAC,KAAK,GAAG,UAAU,GAAG,IAAW;YACzC,MAAM,MAAM,GAAQ,cAAc,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YACrD,MAAM,SAAS,GACb,CAAC,CAAC,MAAM,IAAI,CAAC,OAAO,MAAM,KAAK,QAAQ,IAAI,OAAO,MAAM,KAAK,UAAU,CAAC,IAAI,OAAO,MAAM,CAAC,IAAI,KAAK,UAAU,CAAC;YAChH,OAAO,SAAS;gBACd,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAS,EAAE,EAAE,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBAC/E,CAAC,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAC1D,CAAC,CAAC;IACJ,CAAC,CAAC;AACJ,CAAC;AAdD,kEAcC", "sourcesContent": ["import { ClassTransformer } from '../ClassTransformer';\nimport { ClassTransformOptions } from '../interfaces';\n\n/**\n * Return the class instance only with the exposed properties.\n *\n * Can be applied to functions and getters/setters only.\n */\nexport function TransformInstanceToInstance(params?: ClassTransformOptions): MethodDecorator {\n  return function (target: Record<string, any>, propertyKey: string | Symbol, descriptor: PropertyDescriptor): void {\n    const classTransformer: ClassTransformer = new ClassTransformer();\n    const originalMethod = descriptor.value;\n\n    descriptor.value = function (...args: any[]): Record<string, any> {\n      const result: any = originalMethod.apply(this, args);\n      const isPromise =\n        !!result && (typeof result === 'object' || typeof result === 'function') && typeof result.then === 'function';\n      return isPromise\n        ? result.then((data: any) => classTransformer.instanceToInstance(data, params))\n        : classTransformer.instanceToInstance(result, params);\n    };\n  };\n}\n"]}