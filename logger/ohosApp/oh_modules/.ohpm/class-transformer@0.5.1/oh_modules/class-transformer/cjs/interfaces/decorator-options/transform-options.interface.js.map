{"version": 3, "file": "transform-options.interface.js", "sourceRoot": "", "sources": ["../../../../src/interfaces/decorator-options/transform-options.interface.ts"], "names": [], "mappings": "", "sourcesContent": ["/**\n * Possible transformation options for the @Transform decorator.\n */\nexport interface TransformOptions {\n  /**\n   * First version where this property should be exposed.\n   *\n   * Example:\n   * ```ts\n   * instanceToPlain(payload, { version: 1.0 });\n   * ```\n   */\n  since?: number;\n\n  /**\n   * Last version where this property should be exposed.\n   *\n   * Example:\n   * ```ts\n   * instanceToPlain(payload, { version: 1.0 });\n   * ```\n   */\n  until?: number;\n\n  /**\n   * List of transformation groups this property belongs to. When set,\n   * the property will be exposed only when transform is called with\n   * one of the groups specified.\n   *\n   * Example:\n   * ```ts\n   * instanceToPlain(payload, { groups: ['user'] });\n   * ```\n   */\n  groups?: string[];\n\n  /**\n   * Expose this property only when transforming from plain to class instance.\n   */\n  toClassOnly?: boolean;\n\n  /**\n   * Expose this property only when transforming from class instance to plain object.\n   */\n  toPlainOnly?: boolean;\n}\n"]}