import { getGlobal } from '.';
describe('getGlobal()', () => {
    it('should return true if <PERSON><PERSON><PERSON> is present in globalThis', () => {
        expect(getGlobal().Buffer).toBe(true);
    });
    it('should return false if <PERSON><PERSON><PERSON> is not present in globalThis', () => {
        const bufferImp = global.Buffer;
        delete global.Buffer;
        expect(getGlobal().Buffer).toBe(false);
        global.Buffer = bufferImp;
    });
});
//# sourceMappingURL=get-global.util.spect.js.map