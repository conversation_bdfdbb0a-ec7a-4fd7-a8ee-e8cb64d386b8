{"version": 3, "file": "default-options.constant.js", "sourceRoot": "", "sources": ["../../../src/constants/default-options.constant.ts"], "names": [], "mappings": "AAEA;;GAEG;AACH,MAAM,CAAC,MAAM,cAAc,GAAmC;IAC5D,mBAAmB,EAAE,KAAK;IAC1B,wBAAwB,EAAE,KAAK;IAC/B,uBAAuB,EAAE,KAAK;IAC9B,eAAe,EAAE,SAAS;IAC1B,mBAAmB,EAAE,KAAK;IAC1B,iBAAiB,EAAE,IAAI;IACvB,MAAM,EAAE,SAAS;IACjB,gBAAgB,EAAE,KAAK;IACvB,QAAQ,EAAE,SAAS;IACnB,UAAU,EAAE,SAAS;IACrB,OAAO,EAAE,SAAS;CACnB,CAAC", "sourcesContent": ["import { ClassTransformOptions } from '../interfaces/class-transformer-options.interface';\n\n/**\n * These are the default options used by any transformation operation.\n */\nexport const defaultOptions: Partial<ClassTransformOptions> = {\n  enableCircularCheck: false,\n  enableImplicitConversion: false,\n  excludeExtraneousValues: false,\n  excludePrefixes: undefined,\n  exposeDefaultValues: false,\n  exposeUnsetFields: true,\n  groups: undefined,\n  ignoreDecorators: false,\n  strategy: undefined,\n  targetMaps: undefined,\n  version: undefined,\n};\n"]}