// automatically generated by the FlatBuffers compiler, do not modify

/* eslint-disable @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any, @typescript-eslint/no-non-null-assertion */

import * as flatbuffers from '@ohos/flatbuffers';

import { LoggerContainer } from './logger-container';


export class LoggerMessage {
  bb: flatbuffers.ByteBuffer|null = null;
  bb_pos = 0;
  __init(i:number, bb:flatbuffers.ByteBuffer):LoggerMessage {
  this.bb_pos = i;
  this.bb = bb;
  return this;
}

static getRootAsLoggerMessage(bb:flatbuffers.ByteBuffer, obj?:LoggerMessage):LoggerMessage {
  return (obj || new LoggerMessage()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
}

static getSizePrefixedRootAsLoggerMessage(bb:flatbuffers.ByteBuffer, obj?:LoggerMessage):LoggerMessage {
  bb.setPosition(bb.position() + flatbuffers.SIZE_PREFIX_LENGTH);
  return (obj || new LoggerMessage()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
}

code():number {
  const offset = this.bb!.__offset(this.bb_pos, 4);
  return offset ? this.bb!.readInt32(this.bb_pos + offset) : 0;
}

containerType():LoggerContainer {
  const offset = this.bb!.__offset(this.bb_pos, 6);
  return offset ? this.bb!.readUint8(this.bb_pos + offset) : LoggerContainer.NONE;
}

container(obj: flatbuffers.Table): flatbuffers.Table|null {
  const offset = this.bb!.__offset(this.bb_pos, 8);
  return offset ? this.bb!.__union(obj, this.bb_pos + offset) : null;
}

static startLoggerMessage(builder:flatbuffers.Builder) {
  builder.startObject(3);
}

static addCode(builder:flatbuffers.Builder, code:number) {
  builder.addFieldInt32(0, code, 0);
}

static addContainerType(builder:flatbuffers.Builder, containerType:LoggerContainer) {
  builder.addFieldInt8(1, containerType, LoggerContainer.NONE);
}

static addContainer(builder:flatbuffers.Builder, containerOffset:flatbuffers.Offset) {
  builder.addFieldOffset(2, containerOffset, 0);
}

static endLoggerMessage(builder:flatbuffers.Builder):flatbuffers.Offset {
  const offset = builder.endObject();
  return offset;
}

static createLoggerMessage(builder:flatbuffers.Builder, code:number, containerType:LoggerContainer, containerOffset:flatbuffers.Offset):flatbuffers.Offset {
  LoggerMessage.startLoggerMessage(builder);
  LoggerMessage.addCode(builder, code);
  LoggerMessage.addContainerType(builder, containerType);
  LoggerMessage.addContainer(builder, containerOffset);
  return LoggerMessage.endLoggerMessage(builder);
}
}
