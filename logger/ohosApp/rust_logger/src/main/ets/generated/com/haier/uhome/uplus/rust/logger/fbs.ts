// FlatBuffer generated types for Logger (HarmonyOS compatible)

/* eslint-disable @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any, @typescript-eslint/no-non-null-assertion */

// Only export the types that are actually used in the HarmonyOS bridge layer
export { <PERSON><PERSON>Wrapper } from './fbs/bool-wrapper';
export { Int32Wrapper } from './fbs/int32-wrapper';
export { LoggerContainer } from './fbs/logger-container';
export { LoggerFlat } from './fbs/logger-flat';
export { LoggerMessage } from './fbs/logger-message';
export { StrWrapper } from './fbs/str-wrapper';
