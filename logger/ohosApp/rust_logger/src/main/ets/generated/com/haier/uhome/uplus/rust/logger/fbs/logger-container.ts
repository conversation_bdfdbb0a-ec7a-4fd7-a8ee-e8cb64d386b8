// automatically generated by the FlatBuffers compiler, do not modify

/* eslint-disable @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any, @typescript-eslint/no-non-null-assertion */

import { BoolWrapper } from './bool-wrapper';
import { Int32Wrapper } from './int32-wrapper';
import { StrWrapper } from './str-wrapper';


export enum LoggerContainer {
  NONE = 0,
  BoolWrapper = 1,
  StrWrapper = 2,
  Int32Wrapper = 3,
  LargeFileEvent = 9
}

export function unionToLoggerContainer(
  type: LoggerContainer,
  accessor: (obj:BoolWrapper|Int32Wrapper|StrWrapper) => BoolWrapper|Int32Wrapper|StrWrapper|null
): BoolWrapper|Int32Wrapper|StrWrapper|null {
  switch(LoggerContainer[type]) {
    case 'NONE': return null;
    case 'BoolWrapper': return accessor(new BoolWrapper())! as Bo<PERSON>Wrapper;
    case 'StrWrapper': return accessor(new StrWrapper())! as StrWrapper;
    case 'Int32Wrapper': return accessor(new Int32Wrapper())! as Int32Wrapper;
    case 'LargeFileEvent': return null; // 前端不需要解析LargeFileEvent
    default: return null;
  }
}

export function unionListToLoggerContainer(
  type: LoggerContainer,
  accessor: (index: number, obj:BoolWrapper|Int32Wrapper|StrWrapper) => BoolWrapper|Int32Wrapper|StrWrapper|null,
  index: number
): BoolWrapper|Int32Wrapper|StrWrapper|null {
  switch(LoggerContainer[type]) {
    case 'NONE': return null;
    case 'BoolWrapper': return accessor(index, new BoolWrapper())! as BoolWrapper;
    case 'StrWrapper': return accessor(index, new StrWrapper())! as StrWrapper;
    case 'Int32Wrapper': return accessor(index, new Int32Wrapper())! as Int32Wrapper;
    case 'LargeFileEvent': return null; // 前端不需要解析LargeFileEvent
    default: return null;
  }
}
