/**
 * Logger常量定义
 */
export class LoggerConstant {
  // 库名称
  static readonly LibName: string = "lib_logger";
  
  // 参数键名
  static readonly Action: string = "action";
  static readonly ListenerId: string = "listener_id";
}

/**
 * Logger错误码
 */
export enum LoggerCode {
  Success = 0,
  RustCallFailure = -1,
  InvalidParameter = -2,
  NotInitialized = -3,
  FileOperationFailed = -4,
  CompressionFailed = -5,
  NetworkError = -6,
  UnknownError = -999
}

/**
 * Logger错误消息
 */
export class LoggerErrorMessage {
  static readonly Success: string = "Success";
  static readonly RustCallFailure: string = "Rust call failure";
  static readonly InvalidParameter: string = "Invalid parameter";
  static readonly NotInitialized: string = "Logger not initialized";
  static readonly FileOperationFailed: string = "File operation failed";
  static readonly CompressionFailed: string = "Compression failed";
  static readonly NetworkError: string = "Network error";
  static readonly UnknownError: string = "Unknown error";
}
