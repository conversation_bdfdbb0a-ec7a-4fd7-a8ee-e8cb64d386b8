export class LoggerResult<T> {
  success: boolean;
  code: number;
  message: string;
  data?: T;

  constructor(success: boolean, code: number, message: string, data?: T) {
    this.success = success;
    this.code = code;
    this.message = message;
    this.data = data;
  }

  isSuccess(): boolean {
    return this.code === LoggerResultCode.Success;
  }

  isFailure(): boolean {
    return this.code !== LoggerResultCode.Success;
  }
}

export enum LoggerResultCode {
  Success = 0,
  ArgumentError = -1,
  JniEnvError = -2,
  JniReturnNull = -3,
  InitializationError = -4,
  NotInitialized = -5,
}
