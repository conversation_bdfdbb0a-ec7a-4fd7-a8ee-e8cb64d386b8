export enum LogLevel {
  TRACE = 0,
  DEBUG = 1,
  INFO = 2,
  WARN = 3,
  ERROR = 4
}

/**
 * 配置对象接口
 */
interface LoggerConfigObject {
  logLevel: LogLevel;
  enableConsoleOutput: boolean;
  enableFullLog: boolean;
  testMode: boolean;
  logEnv: string;
  disableSensitiveWords: boolean;
  userId: string;
  deviceId: string;
  sessionId: string;
  appVersion: string;
  privacyAgreed: boolean;
  isDebugMode: boolean;
  maxFileSize: number;
  maxDirectorySize: number;
  logFilePrefix: string;
  logDirectory: string;
  customPrefix: string;
  maxLogLength: number;
  maxLogsPerSecond: number;
  bufferSize: number;
  versionName: string;
}

export class LoggerConfig {
  // 基础配置
  logLevel: LogLevel = LogLevel.WARN;
  enableConsoleOutput: boolean = false;  // 默认关闭控制台输出（对应Android默认值）
  enableFullLog: boolean = false;        // 全量日志开关（对应Android的enableFullLogs）
  testMode: boolean = false;
  logEnv: string = "SC"; // SC: 生产环境, YS: 验收环境
  disableSensitiveWords: boolean = false; // 是否禁用脱敏

  // 用户信息
  userId: string = "0";
  deviceId: string = "";
  sessionId: string = "";
  appVersion: string = "";

  // 隐私配置
  privacyAgreed: boolean = false;
  isDebugMode: boolean = false;

  // 文件配置
  maxFileSize: number = 20 * 1024 * 1024; // 20MB
  maxDirectorySize: number = 600 * 1024 * 1024; // 600MB
  logFilePrefix: string = "uplog";
  logDirectory: string = ""; // 必须设置

  // 格式化配置
  customPrefix: string = "";

  // 性能配置
  maxLogLength: number = 4000;
  maxLogsPerSecond: number = 2000;
  bufferSize: number = 8192;

  // HarmonyOS对齐的关键配置
  versionName: string = ""; // 版本名称，用作一级目录

  constructor() {
    // 默认构造函数
  }

  /**
   * 从对象创建配置
   */
  static fromObject(obj: Record<string, Object>): LoggerConfig {
    const config = new LoggerConfig();
    
    // 基础配置
    if (obj.logLevel !== undefined) config.logLevel = obj.logLevel as LogLevel;
    if (obj.enableConsoleOutput !== undefined) config.enableConsoleOutput = obj.enableConsoleOutput as boolean;
    if (obj.enableFullLog !== undefined) config.enableFullLog = obj.enableFullLog as boolean;
    if (obj.testMode !== undefined) config.testMode = obj.testMode as boolean;
    if (obj.logEnv !== undefined) config.logEnv = obj.logEnv as string;
    if (obj.disableSensitiveWords !== undefined) config.disableSensitiveWords = obj.disableSensitiveWords as boolean;

    // 用户信息
    if (obj.userId !== undefined) config.userId = obj.userId as string;
    if (obj.deviceId !== undefined) config.deviceId = obj.deviceId as string;
    if (obj.sessionId !== undefined) config.sessionId = obj.sessionId as string;
    if (obj.appVersion !== undefined) config.appVersion = obj.appVersion as string;

    // 隐私配置
    if (obj.privacyAgreed !== undefined) config.privacyAgreed = obj.privacyAgreed as boolean;
    if (obj.isDebugMode !== undefined) config.isDebugMode = obj.isDebugMode as boolean;

    // 文件配置
    if (obj.maxFileSize !== undefined) config.maxFileSize = obj.maxFileSize as number;
    if (obj.maxDirectorySize !== undefined) config.maxDirectorySize = obj.maxDirectorySize as number;
    if (obj.logFilePrefix !== undefined) config.logFilePrefix = obj.logFilePrefix as string;
    if (obj.logDirectory !== undefined) config.logDirectory = obj.logDirectory as string;

    // 格式化配置
    if (obj.customPrefix !== undefined) config.customPrefix = obj.customPrefix as string;

    // 性能配置
    if (obj.maxLogLength !== undefined) config.maxLogLength = obj.maxLogLength as number;
    if (obj.maxLogsPerSecond !== undefined) config.maxLogsPerSecond = obj.maxLogsPerSecond as number;
    if (obj.bufferSize !== undefined) config.bufferSize = obj.bufferSize as number;

    // HarmonyOS对齐的关键配置
    if (obj.versionName !== undefined) config.versionName = obj.versionName as string;

    return config;
  }

  /**
   * 转换为普通对象
   */
  toObject(): LoggerConfigObject {
    return {
      // 基础配置
      logLevel: this.logLevel,
      enableConsoleOutput: this.enableConsoleOutput,
      enableFullLog: this.enableFullLog,
      testMode: this.testMode,
      logEnv: this.logEnv,
      disableSensitiveWords: this.disableSensitiveWords,

      // 用户信息
      userId: this.userId,
      deviceId: this.deviceId,
      sessionId: this.sessionId,
      appVersion: this.appVersion,

      // 隐私配置
      privacyAgreed: this.privacyAgreed,
      isDebugMode: this.isDebugMode,

      // 文件配置
      maxFileSize: this.maxFileSize,
      maxDirectorySize: this.maxDirectorySize,
      logFilePrefix: this.logFilePrefix,
      logDirectory: this.logDirectory,

      // 格式化配置
      customPrefix: this.customPrefix,

      // 性能配置
      maxLogLength: this.maxLogLength,
      maxLogsPerSecond: this.maxLogsPerSecond,
      bufferSize: this.bufferSize,

      // HarmonyOS对齐的关键配置
      versionName: this.versionName
    };
  }
}
