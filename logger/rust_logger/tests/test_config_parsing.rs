use rust_logger::LoggerConfig;
use rust_logger::core::LogLevel;

#[test]
fn test_log_level_serialization() {
    // 测试LogLevel的序列化和反序列化
    let level = LogLevel::Info;
    let json = serde_json::to_string(&level).expect("Failed to serialize LogLevel");
    println!("LogLevel::Info serialized as: {}", json);

    let deserialized: LogLevel = serde_json::from_str(&json).expect("Failed to deserialize LogLevel");
    assert_eq!(deserialized, LogLevel::Info);

    // 测试从数字反序列化
    let from_number: LogLevel = serde_json::from_str("1").expect("Failed to deserialize from number");
    assert_eq!(from_number, LogLevel::Info);
}

#[test]
fn test_config_parsing_snake_case() {
    // 测试snake_case字段名（Rust原生格式）
    let json = r#"{
        "log_level": 1,
        "enable_console_output": true,
        "enable_full_log": false,
        "test_mode": false,
        "log_env": "SC",
        "disable_sensitive_words": false,
        "user_id": "test_user",
        "device_id": "test_device",
        "session_id": "test_session",
        "app_version": "1.0.0",
        "privacy_agreed": true,
        "is_debug_mode": false,
        "max_file_size": 20971520,
        "max_directory_size": 629145600,
        "log_file_prefix": "uplog",
        "log_directory": "/tmp/test_logs",
        "custom_prefix": "test",
        "max_log_length": 4000,
        "max_logs_per_second": 2000,
        "buffer_size": 8192,
        "version_name": "1.0.0"
    }"#;

    let config = LoggerConfig::from_json(json).expect("Failed to parse snake_case config");
    assert_eq!(config.log_level, LogLevel::Info);
    assert_eq!(config.enable_console_output, true);
    assert_eq!(config.user_id, "test_user");
    assert_eq!(config.log_directory, "/tmp/test_logs");
}

#[test]
fn test_config_parsing_camel_case() {
    // 测试camelCase字段名（HarmonyOS/Android格式）
    let json = r#"{
        "logLevel": 2,
        "enableConsoleOutput": false,
        "enableFullLog": true,
        "testMode": true,
        "logEnv": "YS",
        "disableSensitiveWords": true,
        "userId": "harmony_user",
        "deviceId": "harmony_device",
        "sessionId": "harmony_session",
        "appVersion": "2.0.0",
        "privacyAgreed": true,
        "isDebugMode": true,
        "maxFileSize": 10485760,
        "maxDirectorySize": 314572800,
        "logFilePrefix": "harmonylog",
        "logDirectory": "/data/logs",
        "customPrefix": "harmony",
        "maxLogLength": 3000,
        "maxLogsPerSecond": 1500,
        "bufferSize": 4096,
        "versionName": "2.0.0"
    }"#;

    let config = LoggerConfig::from_json(json).expect("Failed to parse camelCase config");
    assert_eq!(config.log_level, LogLevel::Warn);
    assert_eq!(config.enable_console_output, false);
    assert_eq!(config.enable_full_log, true);
    assert_eq!(config.user_id, "harmony_user");
    assert_eq!(config.log_directory, "/data/logs");
    assert_eq!(config.version_name, Some("2.0.0".to_string()));
}

#[test]
fn test_config_parsing_mixed_case() {
    // 测试混合字段名（部分snake_case，部分camelCase）
    let json = r#"{
        "log_level": 0,
        "enableConsoleOutput": true,
        "enable_full_log": false,
        "testMode": false,
        "log_env": "SC",
        "disableSensitiveWords": false,
        "user_id": "mixed_user",
        "deviceId": "mixed_device",
        "session_id": "mixed_session",
        "appVersion": "1.5.0",
        "privacy_agreed": true,
        "isDebugMode": false,
        "max_file_size": 15728640,
        "maxDirectorySize": 471859200,
        "log_file_prefix": "mixedlog",
        "logDirectory": "/var/logs",
        "custom_prefix": "mixed",
        "maxLogLength": 3500,
        "max_logs_per_second": 1800,
        "bufferSize": 6144,
        "version_name": "1.5.0"
    }"#;

    let config = LoggerConfig::from_json(json).expect("Failed to parse mixed case config");
    assert_eq!(config.log_level, LogLevel::Debug);
    assert_eq!(config.enable_console_output, true);
    assert_eq!(config.enable_full_log, false);
    assert_eq!(config.user_id, "mixed_user");
    assert_eq!(config.device_id, Some("mixed_device".to_string()));
    assert_eq!(config.log_directory, "/var/logs");
}

#[test]
fn test_config_parsing_harmony_format() {
    // 测试HarmonyOS实际传递的配置格式
    let json = r#"{
        "logLevel": 2,
        "enableConsoleOutput": true,
        "enableFullLog": false,
        "testMode": false,
        "logEnv": "SC",
        "disableSensitiveWords": false,
        "userId": "harmony_user_123",
        "deviceId": "harmony_device_456",
        "sessionId": "harmony_session_789",
        "appVersion": "1.0.0",
        "privacyAgreed": true,
        "isDebugMode": false,
        "maxFileSize": 20971520,
        "maxDirectorySize": 629145600,
        "logFilePrefix": "uplog",
        "logDirectory": "/data/storage/el2/base/haps/entry/files/logs",
        "customPrefix": null,
        "maxLogLength": 4000,
        "maxLogsPerSecond": 2000,
        "bufferSize": 8192,
        "versionName": "1.0.0"
    }"#;

    let config = LoggerConfig::from_json(json).expect("Failed to parse HarmonyOS config");
    assert_eq!(config.log_level, LogLevel::Warn);
    assert_eq!(config.enable_console_output, true);
    assert_eq!(config.enable_full_log, false);
    assert_eq!(config.user_id, "harmony_user_123");
    assert_eq!(config.device_id, Some("harmony_device_456".to_string()));
    assert_eq!(config.log_directory, "/data/storage/el2/base/haps/entry/files/logs");
    assert_eq!(config.version_name, Some("1.0.0".to_string()));
}

#[test]
fn test_config_parsing_error() {
    // 测试无效JSON
    let invalid_json = r#"{
        "logLevel": "invalid_level",
        "logDirectory": "/tmp/logs"
    }"#;

    let result = LoggerConfig::from_json(invalid_json);
    assert!(result.is_err(), "Should fail to parse invalid JSON");
}

#[test]
fn test_config_to_json() {
    // 测试配置序列化为JSON
    let mut config = LoggerConfig::default();
    config.log_level = LogLevel::Debug;
    config.enable_console_output = true;
    config.user_id = "test_user".to_string();
    config.log_directory = "/tmp/test".to_string();

    let json = config.to_json().expect("Failed to serialize config");
    
    // 验证可以重新解析
    let parsed_config = LoggerConfig::from_json(&json).expect("Failed to parse serialized config");
    assert_eq!(parsed_config.log_level, LogLevel::Debug);
    assert_eq!(parsed_config.enable_console_output, true);
    assert_eq!(parsed_config.user_id, "test_user");
    assert_eq!(parsed_config.log_directory, "/tmp/test");
}
