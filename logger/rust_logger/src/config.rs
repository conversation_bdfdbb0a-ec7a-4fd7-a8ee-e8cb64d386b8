use serde::{Deserialize, Serialize};
use crate::{Log<PERSON><PERSON>l, Result, LoggerError};

/// 日志环境定义
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum LogEnv {
    /// 验收环境
    YS,
    /// 生产环境
    SC,
}

impl Default for LogEnv {
    fn default() -> Self {
        LogEnv::SC
    }
}



/// 日志库配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoggerConfig {
    // 基础配置
    #[serde(alias = "logLevel")]
    pub log_level: LogLevel,
    #[serde(alias = "enableConsoleOutput")]
    pub enable_console_output: bool,  // 控制台输出开关（对应Android的enableConsoleLog）
    #[serde(alias = "enableFullLog")]
    pub enable_full_log: bool,        // 全量日志开关（对应Android的enableFullLogs，true时设置级别为debug + 启用自动上传）
    #[serde(alias = "testMode")]
    pub test_mode: bool,
    #[serde(alias = "logEnv")]
    pub log_env: LogEnv,
    #[serde(alias = "disableSensitiveWords")]
    pub disable_sensitive_words: bool, // 是否禁用日志脱敏，true表示不脱敏

    // 用户信息
    #[serde(alias = "userId")]
    pub user_id: String,
    #[serde(alias = "deviceId")]
    pub device_id: Option<String>,
    #[serde(alias = "sessionId")]
    pub session_id: String,
    #[serde(alias = "appVersion")]
    pub app_version: Option<String>, // 应用版本号，由外部传入

    // 隐私配置
    #[serde(alias = "privacyAgreed")]
    pub privacy_agreed: bool,
    #[serde(alias = "isDebugMode")]
    pub is_debug_mode: bool,

    // 文件配置
    #[serde(alias = "maxFileSize")]
    pub max_file_size: u64,           // 单个文件大小限制，超过时自动上传（默认20MB）
    #[serde(alias = "maxDirectorySize")]
    pub max_directory_size: u64,
    #[serde(alias = "logFilePrefix")]
    pub log_file_prefix: String,
    #[serde(alias = "logDirectory")]
    pub log_directory: String,

    // 格式化配置
    #[serde(alias = "customPrefix")]
    pub custom_prefix: Option<String>,

    // 性能配置
    #[serde(alias = "maxLogLength")]
    pub max_log_length: usize,
    #[serde(alias = "maxLogsPerSecond")]
    pub max_logs_per_second: u32,
    #[serde(alias = "bufferSize")]
    pub buffer_size: usize,

    // Android对齐的关键配置
    #[serde(alias = "versionName")]
    pub version_name: Option<String>,        // 对应setVersionName，用作一级目录
}

impl Default for LoggerConfig {
    fn default() -> Self {
        Self {
            // 基础配置
            log_level: LogLevel::Warn,
            enable_console_output: false,    // 默认关闭控制台输出（对应Android默认值）
            enable_full_log: false,
            test_mode: false,
            log_env: LogEnv::default(),
            disable_sensitive_words: false, // 默认启用脱敏
            
            // 用户信息
            user_id: "0".to_string(), // 默认值为"0"
            device_id: None,
            session_id: uuid::Uuid::new_v4().to_string(),
            app_version: None, // 应用版本号，由外部传入

            // 隐私配置
            privacy_agreed: false,
            is_debug_mode: false,
            
            // 文件配置
            max_file_size: 20 * 1024 * 1024, // 20MB，超过时自动上传
            max_directory_size: 600 * 1024 * 1024, // 600MB (与Android DEFAULT_TOTAL_LOG_FILE_SIZE一致)
            log_file_prefix: "uplog".to_string(),
            log_directory: String::new(), // 必须由外部设置，不提供默认值
            
            // 格式化配置
            custom_prefix: None,
            
            // 性能配置
            max_log_length: 4000,
            max_logs_per_second: 2000,
            buffer_size: 8192, // 8KB，与Rust标准库BufWriter默认值一致

            // Android对齐的关键配置
            version_name: None,                  // 版本名称，用作一级目录
        }
    }
}

impl LoggerConfig {
    /// 创建新的配置
    pub fn new() -> Self {
        Self::default()
    }
    
    /// 从JSON字符串创建配置
    pub fn from_json(json: &str) -> Result<Self> {
        serde_json::from_str(json).map_err(LoggerError::from)
    }
    
    /// 转换为JSON字符串
    pub fn to_json(&self) -> Result<String> {
        serde_json::to_string(self).map_err(LoggerError::from)
    }
    
    /// 验证配置的有效性
    pub fn validate(&self) -> Result<()> {
        if self.log_directory.is_empty() {
            return Err(LoggerError::config("Log directory must be set"));
        }

        if self.max_log_length == 0 || self.max_log_length > 4000 {
            return Err(LoggerError::config("max_log_length must be between 1 and 4000"));
        }
        
        if self.max_logs_per_second == 0 || self.max_logs_per_second > 5000 {
            return Err(LoggerError::config("max_logs_per_second must be between 1 and 5000"));
        }

        if self.buffer_size < 512 || self.buffer_size > 65536 {
            return Err(LoggerError::config("buffer_size must be between 512 and 65536"));
        }
        
        if self.max_file_size < 5 * 1024 * 1024 || self.max_file_size > 50 * 1024 * 1024 {
            return Err(LoggerError::config("max_file_size must be between 5MB and 50MB"));
        }

        if self.max_directory_size < 100 * 1024 * 1024 || self.max_directory_size > 600 * 1024 * 1024 {
            return Err(LoggerError::config("max_directory_size must be between 100MB and 600MB"));
        }

        if self.log_file_prefix.is_empty() {
            return Err(LoggerError::config("log_file_prefix cannot be empty"));
        }
        
        Ok(())
    }
    

    
    /// 设置用户ID
    pub fn set_user_id<S: Into<String>>(&mut self, user_id: S) {
        self.user_id = user_id.into();
    }
    
    /// 设置设备ID
    pub fn set_device_id<S: Into<String>>(&mut self, device_id: S) {
        self.device_id = Some(device_id.into());
    }
    
    /// 设置隐私协议状态
    pub fn set_privacy_agreed(&mut self, agreed: bool) {
        self.privacy_agreed = agreed;
    }
    
    /// 设置全量日志状态
    pub fn set_full_log(&mut self, enable: bool) {
        self.enable_full_log = enable;
    }
    
    /// 设置日志级别
    pub fn set_log_level(&mut self, level: LogLevel) {
        self.log_level = level;
    }

    /// 设置测试模式
    pub fn set_test_mode(&mut self, test_mode: bool) {
        self.test_mode = test_mode;
    }

    /// 设置日志环境
    pub fn set_log_env(&mut self, env: LogEnv) {
        self.log_env = env;
    }

    /// 设置日志脱敏开关
    pub fn set_disable_sensitive_words(&mut self, disable: bool) {
        self.disable_sensitive_words = disable;
    }

    /// 设置单条日志最大长度
    pub fn set_max_log_length(&mut self, max_length: usize) {
        self.max_log_length = max_length.min(4000).max(1); // 限制在1-4000范围内
    }

    /// 设置缓冲区大小
    pub fn set_buffer_size(&mut self, buffer_size: usize) {
        self.buffer_size = buffer_size.min(65536).max(512); // 限制在512-65536范围内
    }

    /// 设置单个文件最大大小
    pub fn set_max_file_size(&mut self, max_file_size: u64) {
        self.max_file_size = max_file_size.min(50 * 1024 * 1024).max(5 * 1024 * 1024); // 限制在5-50MB范围内
    }

    /// 设置目录最大大小
    pub fn set_max_directory_size(&mut self, max_directory_size: u64) {
        self.max_directory_size = max_directory_size.min(600 * 1024 * 1024).max(100 * 1024 * 1024); // 限制在100-600MB范围内
    }



    /// 设置日志存储目录
    pub fn set_log_directory<S: Into<String>>(&mut self, log_directory: S) {
        self.log_directory = log_directory.into();
    }



    /// 获取崩溃日志存储目录
    /// 遵循Android逻辑: path + "/" + "uhomelog" + "/exception"
    pub fn get_crash_log_directory(&self) -> String {
        format!("{}/uhomelog/exception", self.log_directory)
    }
    
    /// 获取有效的日志级别
    /// 根据规则：enableFullLog=true时设置级别为debug
    pub fn effective_log_level(&self) -> LogLevel {
        if self.enable_full_log {
            LogLevel::Debug  // enableFullLog=true时强制为debug
        } else {
            self.log_level
        }
    }

    /// 检查是否应该记录指定级别的日志
    pub fn should_log(&self, level: LogLevel) -> bool {
        self.privacy_agreed && level >= self.effective_log_level()
    }
    
    /// 检查是否启用自动上传
    /// 当enable_full_log=true且文件超过max_file_size时触发
    pub fn is_auto_upload_enabled(&self) -> bool {
        self.enable_full_log
    }

    /// 设置版本名称
    pub fn set_version_name<S: Into<String>>(&mut self, version_name: Option<S>) {
        self.version_name = version_name.map(|s| s.into());
    }

    /// 获取带版本号的日志目录
    /// 遵循Android逻辑: path + "/" + "uhomelog" + "/" + versionName
    pub fn get_versioned_log_directory(&self) -> String {
        let base_path = format!("{}/uhomelog", self.log_directory);
        if let Some(ref version) = self.version_name {
            format!("{}/{}", base_path, version)
        } else {
            base_path
        }
    }
}
