[package]
name = "rust_logger"
version = "1.0.0"
edition = "2021"

[lib]
name = "rust_logger"
crate-type = ["cdylib", "staticlib", "rlib"]

[features]
android = ["dep:jni", "dep:android_logger"]
ohos = ["dep:napi-ohos", "dep:napi-derive-ohos"]
ios = []

[dependencies]
# 平台特定依赖（可选）
jni = { workspace = true, optional = true }
android_logger = { workspace = true, optional = true }
napi-ohos = { workspace = true, optional = true }
napi-derive-ohos = { workspace = true, optional = true }

# 核心依赖
log = { workspace = true, features = ["std"] }
serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true }
chrono = { workspace = true, features = ["serde"] }
uuid = { workspace = true, features = ["v4"] }
regex = { workspace = true }
thiserror = { workspace = true }
parking_lot = { workspace = true }
once_cell = { workspace = true }

# 文件操作
zip = { workspace = true }

# Protobuf支持
prost = { workspace = true }
prost-types = { workspace = true }

# 十六进制编码（用于protobuf数据存储）
hex = { workspace = true }

# FlatBuffers支持
flatbuffers = { workspace = true }

# 公司内部依赖
rust_storage = { path = "../../storage_rust/rust_storage" }
task_manager = { path = "../../task_manager_rust/task_manager_rust" }

# 网络请求已移至前端处理，无需Rust层网络库

[build-dependencies]
prost-build = { workspace = true }
napi-build-ohos = { workspace = true }
cc = { workspace = true }

[dev-dependencies]
env_logger = { workspace = true }
futures = { workspace = true }
tokio = { workspace = true, features = ["rt-multi-thread", "macros"] }
