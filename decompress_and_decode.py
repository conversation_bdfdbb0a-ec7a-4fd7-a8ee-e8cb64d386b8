#!/usr/bin/env python3

import sys
import os
import zlib
import gzip
import bz2

# 添加protobuf生成文件的路径
sys.path.insert(0, 'logger/rust_logger/proto')

try:
    import log_metadata_pb2
    print("✅ Successfully imported log_metadata_pb2")
except ImportError as e:
    print(f"❌ Failed to import log_metadata_pb2: {e}")
    sys.exit(1)

def try_decompress_zlib(data):
    """尝试用zlib解压"""
    print("🔍 Trying zlib decompression...")
    try:
        # 尝试不同的zlib参数
        for wbits in [15, -15, 15 | 16, 15 | 32]:
            try:
                decompressed = zlib.decompress(data, wbits)
                print(f"✅ zlib decompression successful with wbits={wbits}")
                return decompressed
            except zlib.error:
                continue
        print("❌ zlib decompression failed")
        return None
    except Exception as e:
        print(f"❌ zlib decompression error: {e}")
        return None

def try_decompress_gzip(data):
    """尝试用gzip解压"""
    print("🔍 Trying gzip decompression...")
    try:
        decompressed = gzip.decompress(data)
        print("✅ gzip decompression successful")
        return decompressed
    except Exception as e:
        print(f"❌ gzip decompression failed: {e}")
        return None

def try_decompress_bz2(data):
    """尝试用bz2解压"""
    print("🔍 Trying bz2 decompression...")
    try:
        decompressed = bz2.decompress(data)
        print("✅ bz2 decompression successful")
        return decompressed
    except Exception as e:
        print(f"❌ bz2 decompression failed: {e}")
        return None

def try_parse_protobuf(data):
    """尝试解析protobuf数据"""
    print(f"🔍 Trying to parse {len(data)} bytes as protobuf...")
    
    try:
        log_entry = log_metadata_pb2.LogMetaData()
        log_entry.ParseFromString(data)
        print(f"✅ Successfully parsed protobuf message")
        return [log_entry]
    except Exception as e:
        print(f"❌ Failed to parse protobuf: {e}")
        return []

def try_parse_text(data):
    """尝试解析为文本"""
    print(f"🔍 Trying to parse {len(data)} bytes as text...")
    
    try:
        # 尝试不同的编码
        for encoding in ['utf-8', 'latin-1', 'ascii', 'gbk']:
            try:
                text = data.decode(encoding)
                if text.strip() and all(ord(c) < 128 or c.isprintable() for c in text[:100]):
                    print(f"✅ Successfully decoded as {encoding}")
                    return text
            except (UnicodeDecodeError, UnicodeError):
                continue
        
        print("❌ Failed to decode as readable text")
        return None
    except Exception as e:
        print(f"❌ Error parsing as text: {e}")
        return None

def analyze_data_pattern(data):
    """分析数据模式"""
    print(f"🔍 Analyzing data pattern...")
    print(f"📊 Data length: {len(data)} bytes")
    print(f"📋 First 32 bytes: {' '.join(f'{b:02x}' for b in data[:32])}")
    print(f"📋 Last 32 bytes: {' '.join(f'{b:02x}' for b in data[-32:])}")
    
    # 检查是否有重复模式
    if len(data) >= 4:
        # 检查是否有0x00 0x00 0x00 0x00模式
        null_pattern = b'\x00\x00\x00\x00'
        if null_pattern in data:
            positions = []
            start = 0
            while True:
                pos = data.find(null_pattern, start)
                if pos == -1:
                    break
                positions.append(pos)
                start = pos + 1
            print(f"🎯 Found null patterns at positions: {positions}")
    
    # 检查是否有0xff 0xff模式
    if b'\xff\xff' in data:
        positions = []
        start = 0
        while True:
            pos = data.find(b'\xff\xff', start)
            if pos == -1:
                break
            positions.append(pos)
            start = pos + 1
        print(f"🎯 Found 0xff 0xff patterns at positions: {positions}")

def main():
    if len(sys.argv) != 2:
        print("Usage: python3 decompress_and_decode.py <decoded_log_file>")
        print("Example: python3 decompress_and_decode.py /Users/<USER>/Downloads/uplog_20250620.xlog.log")
        sys.exit(1)
    
    log_file = sys.argv[1]
    
    if not os.path.exists(log_file):
        print(f"❌ File not found: {log_file}")
        sys.exit(1)
    
    print(f"🔧 Processing file: {log_file}")
    
    with open(log_file, 'rb') as f:
        original_data = f.read()
    
    print(f"📁 Original file size: {len(original_data)} bytes")
    
    # 分析原始数据
    analyze_data_pattern(original_data)
    
    # 尝试不同的解压方法
    decompressed_data = None
    
    # 1. 尝试zlib解压
    decompressed_data = try_decompress_zlib(original_data)
    
    # 2. 如果zlib失败，尝试gzip
    if decompressed_data is None:
        decompressed_data = try_decompress_gzip(original_data)
    
    # 3. 如果gzip失败，尝试bz2
    if decompressed_data is None:
        decompressed_data = try_decompress_bz2(original_data)
    
    # 如果解压成功，分析解压后的数据
    if decompressed_data is not None:
        print(f"\n🎉 Decompression successful! Decompressed size: {len(decompressed_data)} bytes")
        analyze_data_pattern(decompressed_data)
        
        # 尝试解析解压后的数据
        print("\n" + "="*60)
        print("🔍 Trying to parse decompressed data as protobuf...")
        messages = try_parse_protobuf(decompressed_data)
        
        if messages:
            print(f"🎉 Successfully parsed {len(messages)} protobuf messages!")
            for i, msg in enumerate(messages, 1):
                print(f"\n📝 Log Entry #{i}:")
                print(f"   Time: {msg.time}")
                print(f"   Level: {msg.level}")
                print(f"   Tag: {msg.tag}")
                print(f"   Session ID: {msg.session_id}")
                print(f"   User ID: {msg.user_id}")
                print(f"   Test Mode: {msg.test_mode}")
                print(f"   Message: {msg.log_message}")
        else:
            print("🔍 Trying to parse decompressed data as text...")
            text = try_parse_text(decompressed_data)
            if text:
                print(f"📝 Text content preview: {repr(text[:200])}")
                
                # 保存解压后的文本
                output_file = log_file + ".decompressed.txt"
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(text)
                print(f"💾 Decompressed text saved to: {output_file}")
            else:
                # 保存解压后的二进制数据用于进一步分析
                output_file = log_file + ".decompressed.bin"
                with open(output_file, 'wb') as f:
                    f.write(decompressed_data)
                print(f"💾 Decompressed binary data saved to: {output_file}")
    else:
        print("\n❌ All decompression methods failed")
        print("💡 The data might be:")
        print("   1. Already decompressed")
        print("   2. Using a custom compression format")
        print("   3. Not compressed at all")
        print("   4. Corrupted")
        
        # 尝试直接解析原始数据
        print("\n" + "="*60)
        print("🔍 Trying to parse original data as protobuf...")
        messages = try_parse_protobuf(original_data)
        
        if not messages:
            print("🔍 Trying to parse original data as text...")
            text = try_parse_text(original_data)
            if text:
                print(f"📝 Text content preview: {repr(text[:200])}")

if __name__ == "__main__":
    main()
