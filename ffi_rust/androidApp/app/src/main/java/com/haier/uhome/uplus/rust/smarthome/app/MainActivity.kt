package com.haier.uhome.uplus.rust.smarthome.app

import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.haier.uhome.uplus.rust.channels.RustChannel
import com.haier.uhome.uplus.rust.channels.RustPanic
import com.haier.uhome.uplus.rust.smarthome.app.databinding.ActivityMainBinding

class MainActivity : AppCompatActivity() {
    companion object {
        private const val TAG = "MainActivity"

        init {
            System.loadLibrary("rust_uplus")
        }
    }

    private val binding by viewBinding<ActivityMainBinding>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding.apply {
            btInit.setOnClickListener {
                val dbPath = getDatabasePath("ignore").parent as String
                RustChannel.getRustBuffer(
                    "lib_storage", mapOf(
                        "action" to "init",
                        "path" to dbPath,
                    )
                )
            }
            btHookPanic.setOnClickListener {
                RustPanic.hookPanic()
            }
            btPanic.setOnClickListener {
                RustPanic.makePanicTest()
            }
        }
    }
}