import { AbilityConstant, ConfigurationConstant, UIAbility, Want } from '@kit.AbilityKit';
import { hilog } from '@kit.PerformanceAnalysisKit';
import { window } from '@kit.ArkUI';
import { RustStorage } from '@uplus/rust_storage';
import { RustChannel } from '@uplus/rust_ffi';

export default class EntryAbility extends UIAbility {
  onCreate(want: Want, launchParam: AbilityConstant.LaunchParam): void {
    this.context.getApplicationContext().setColorMode(ConfigurationConstant.ColorMode.COLOR_MODE_NOT_SET);
    hilog.info(0x0000, 'testTag', '%{public}s', 'Ability onCreate');
    RustStorage.initRustStorage(this.context);
    RustChannel.getInstance().init(this.context, true);

    const params = new Map<string, string>();
    params.set("a", "100");
    params.set("device_ids", "{JSONArray}")
    params.set("listener_id", "0xff01fea8009841cb")
    const buf = RustChannel.getInstance().getRustBuffer("lib_test", params);
    hilog.info(0x0000, 'testTag', 'getRustBuffer: %{public}s', this.bufferString(buf ?? new ArrayBuffer(0)));

    RustChannel.getInstance().getRustBufferAsync("lib_test", params, (buf2) => {
      hilog.info(0x0000, 'testTag', 'getRustBufferAsync: %{public}s', this.bufferString(buf2));
    });

    RustChannel.getInstance().manageRustBufferListener("lib_test", params, (buf3) => {
      hilog.info(0x0000, 'testTag', 'manageRustBufferListener: %{public}s', this.bufferString(buf3));
    });

    const buf5 = RustChannel.getInstance().manageRustBufferListenerWithData("lib_test", params, (buf4) => {
      hilog.info(0x0000, 'testTag', 'manageRustBufferListenerWithData: %{public}s', this.bufferString(buf4));
    });
    hilog.info(0x0000, 'testTag', 'manageRustBufferListenerWithData: %{public}s', this.bufferString(buf5!));
  }

  private bufferString(buf: ArrayBuffer): string {
    const arr = new Int8Array(buf);
    return arr.join(',');
  }

  onDestroy(): void {
    hilog.info(0x0000, 'testTag', '%{public}s', 'Ability onDestroy');
  }

  onWindowStageCreate(windowStage: window.WindowStage): void {
    // Main window is created, set main page for this ability
    hilog.info(0x0000, 'testTag', '%{public}s', 'Ability onWindowStageCreate');

    windowStage.loadContent('pages/Index', (err) => {
      if (err.code) {
        hilog.error(0x0000, 'testTag', 'Failed to load the content. Cause: %{public}s', JSON.stringify(err) ?? '');
        return;
      }
      hilog.info(0x0000, 'testTag', 'Succeeded in loading the content.');
    });
  }

  onWindowStageDestroy(): void {
    // Main window is destroyed, release UI related resources
    hilog.info(0x0000, 'testTag', '%{public}s', 'Ability onWindowStageDestroy');
  }

  onForeground(): void {
    // Ability has brought to foreground
    hilog.info(0x0000, 'testTag', '%{public}s', 'Ability onForeground');
  }

  onBackground(): void {
    // Ability has back to background
    hilog.info(0x0000, 'testTag', '%{public}s', 'Ability onBackground');
  }
}