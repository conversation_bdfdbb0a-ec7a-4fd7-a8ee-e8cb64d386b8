{"app": {"products": [{"name": "default", "signingConfig": "default", "compatibleSdkVersion": "5.0.0(12)", "runtimeOS": "HarmonyOS", "buildOption": {"strictMode": {"caseSensitiveCheck": true, "useNormalizedOHMUrl": true}}}], "buildModeSet": [{"name": "debug"}, {"name": "release"}], "signingConfigs": [{"name": "default", "type": "HarmonyOS", "material": {"certpath": "/Users/<USER>/.ohos/config/default_ohosApp_3y9fnTsmM8R3cggvt3rRJuoobZFyaDxFkcau7qfdbpM=.cer", "storePassword": "0000001B5F383BEB225CA6F46CD5E6C4A5D6D67F57829FC81081AD6269DB22FE73718572913A559A2FB9E5", "keyAlias": "debugKey", "keyPassword": "0000001B9F82592ED9383DD1897FB26EB65139EE9A9BA68B868E6424382422ED9A763932ECD96EC0C280D2", "profile": "/Users/<USER>/.ohos/config/default_ohosApp_3y9fnTsmM8R3cggvt3rRJuoobZFyaDxFkcau7qfdbpM=.p7b", "signAlg": "SHA256withECDSA", "storeFile": "/Users/<USER>/.ohos/config/default_ohosApp_3y9fnTsmM8R3cggvt3rRJuoobZFyaDxFkcau7qfdbpM=.p12"}}]}, "modules": [{"name": "entry", "srcPath": "./entry", "targets": [{"name": "default", "applyToProducts": ["default"]}]}, {"name": "rust_ffi", "srcPath": "./rust_ffi"}]}