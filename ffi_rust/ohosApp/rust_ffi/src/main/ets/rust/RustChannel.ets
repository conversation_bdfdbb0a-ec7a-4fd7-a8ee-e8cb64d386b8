import { instanceToPlain } from 'class-transformer';
import "reflect-metadata";
import rustChannel from 'librust_uplus.so';
import { parseError, RustChannelLog } from '../log/RustChannelLog';
import { FileLogger } from '../log/FileLogger';
import { common } from '@kit.AbilityKit';

const TAG = "RustChannel"

export class RustChannel {
  private static instance: RustChannel;
  fileLogger?: FileLogger;

  private constructor() {
  }

  init(context: common.Context, debug: boolean) {
    this.fileLogger = new FileLogger(context, "rust-ffi-channel", debug)
  }

  /**
   * Rust 同步方法
   */
  getRustBuffer(libName: string, params: Map<string, string>): ArrayBuffer | null {
    try {
      const record = instanceToPlain(params)
      const buf = rustChannel.flatCrossPlatform(libName, record) as ArrayBuffer
      RustChannelLog.debug(TAG, `getRustBuffer: ${libName}, ${JSON.stringify(record)} => ${buf.byteLength}`)
      return buf
    } catch (e) {
      RustChannelLog.warn(TAG, `getRustBuffer: ${parseError(e)}`)
      return null
    }
  }

  /**
   * Rust 异步方法
   */
  getRustBufferAsync(
    libName: string,
    params: Map<string, string>,
    callback: (data: ArrayBuffer) => void,
  ) {
    try {
      const record = instanceToPlain(params)
      rustChannel.flatCrossPlatformAsync(libName, record, callback) as ArrayBuffer
      RustChannelLog.debug(TAG, `getRustBufferAsync: ${libName}, ${JSON.stringify(record)}`)
    } catch (e) {
      RustChannelLog.warn(TAG, `getRustBufferAsync: ${parseError(e)}`)
    }
  }

  /**
   * Rust 监听注册
   */
  manageRustBufferListener(
    libName: string,
    params: Map<string, string>,
    callback: (data: ArrayBuffer) => void,
  ) {
    try {
      const record = instanceToPlain(params)
      rustChannel.flatCrossPlatformConsumer(libName, record, callback)
      RustChannelLog.debug(TAG, `manageRustBufferListener: ${libName}, ${JSON.stringify(record)}`)
    } catch (e) {
      RustChannelLog.warn(TAG, `manageRustBufferListener: ${parseError(e)}`)
    }
  }

  /**
   * Rust 监听注册；带返回数据
   */
  manageRustBufferListenerWithData(
    libName: string,
    params: Map<string, string>,
    callback: (data: ArrayBuffer) => void,
  ): ArrayBuffer | null {
    try {
      const record = instanceToPlain(params)
      const buf = rustChannel.flatCrossPlatformConsumerData(libName, record, callback) as ArrayBuffer
      RustChannelLog.debug(TAG,
        `manageRustBufferListenerWithData: ${libName}, ${JSON.stringify(record)} => ${buf.byteLength}`)
      return buf
    } catch (e) {
      RustChannelLog.warn(TAG, `manageRustBufferListenerWithData: ${parseError(e)}`)
      return null
    }
  }

  public static getInstance(): RustChannel {
    if (!RustChannel.instance) {
      RustChannel.instance = new RustChannel()
    }
    return RustChannel.instance
  }
}