import { hilog } from "@kit.PerformanceAnalysisKit";
import { RustChannel } from "../rust/RustChannel";
import { FileLogger } from "./FileLogger";

const DOMAIN = 0xFFFC;

export class RustChannelLog {
  static debug(tag: string, message: string): void {
    hilog.debug(DOMAIN, tag, message);
    RustChannelLog.fileLogger()?.logToFile(tag, message);
  }

  static info(tag: string, message: string): void {
    hilog.info(DOMAIN, tag, message);
    RustChannelLog.fileLogger()?.logToFile(tag, message);
  }

  static warn(tag: string, message: string): void {
    hilog.warn(DOMAIN, tag, message);
    RustChannelLog.fileLogger()?.logToFile(tag, message);
  }

  static error(tag: string, message: string): void {
    hilog.error(DOMAIN, tag, message);
    RustChannelLog.fileLogger()?.logToFile(tag, message);
  }

  private static fileLogger(): FileLogger | undefined {
    return RustChannel.getInstance().fileLogger;
  }
}

export function parseError(e: Error): string {
  const parts: string[] = [];
  parts.push(`ErrorName=${e.name || "N/A"}`);
  parts.push(`ErrorMessage=${e.message || "N/A"}`);
  if (e.stack) {
    parts.push(`ErrorStack:\n${e.stack}`);
  }
  return parts.join('; ');
}
