import { hilog } from '@kit.PerformanceAnalysisKit';
import { fileIo as fs } from '@kit.CoreFileKit';
import { process } from '@kit.ArkTS';
import { common } from '@kit.AbilityKit';

export class FileLogger {
  private suffix: string;
  private enable: boolean;
  private currentDay: string = "";
  private logFile?: fs.File;
  private context: common.Context;

  constructor(context: common.Context, suffix: string, enable: boolean) {
    this.context = context.getApplicationContext();
    this.suffix = suffix;
    this.enable = enable;
  }

  logToFile(tag: string, msg: string) {
    this.logToFileAsync(tag, msg);
  }

  setWriteEnable(enable: boolean) {
    this.enable = enable;
  }

  private async logToFileAsync(tag: string, msg: string) {
    if (this.enable) {
      const time = this.getLogDate();
      const line = `${time} ${tag}\t${process.pid}-${process.tid} ${msg}\n`;
      try {
        this.writeLog(line);
      } catch (e) {
        hilog.error(0, tag, "logToFileAsync error " + this.parseError(e))
      }
    }
  }

  private writeLog(logLine: string) {
    this.openFile();
    if (this.logFile) {
      fs.writeSync(this.logFile.fd, logLine);
    }
  }

  private openFile() {
    const day = this.getCurrentDateFileName();
    if (day != this.currentDay) {
      this.currentDay = day;
      // 关闭文件；打开新文件
      if (this.logFile) {
        fs.closeSync(this.logFile);
      }
      const logDir = `${this.context.filesDir}/uplog`;
      try {
        fs.mkdirSync(logDir);
      } catch (e) {
        // ignore
      }
      const mod = fs.OpenMode.READ_WRITE | fs.OpenMode.APPEND | fs.OpenMode.CREATE;
      this.logFile = fs.openSync(`${logDir}/${day}`, mod);
    }
  }

  private getCurrentDateFileName(): string {
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需要加1
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}-${this.suffix}.log`;
  }

  private getLogDate(): string {
    const date = new Date();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    const milliseconds = String(date.getMilliseconds()).padStart(3, '0');
    return `${month}-${day} ${hours}:${minutes}:${seconds}:${milliseconds}`;
  }

  private parseError(e: Error): string {
    const parts: string[] = [];
    parts.push(`ErrorName=${e.name || "N/A"}`);
    parts.push(`ErrorMessage=${e.message || "N/A"}`);
    if (e.stack) {
      parts.push(`ErrorStack:\n${e.stack}`);
    }
    return parts.join('; ');
  }
}