// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.5.0.

// ignore_for_file: unused_import, unused_element, unnecessary_import, duplicate_ignore, invalid_use_of_internal_member, annotate_overrides, non_constant_identifier_names, curly_braces_in_flow_control_structures, prefer_const_literals_to_create_immutables, unused_field

import 'dart:async';
import 'dart:convert';
import 'features/flutter/dart.dart';
import 'frb_generated.dart';
import 'frb_generated.io.dart'
    if (dart.library.js_interop) 'frb_generated.web.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';

/// Main entrypoint of the Rust API
class RustLib extends BaseEntrypoint<RustLibApi, RustLibApiImpl, RustLibWire> {
  @internal
  static final instance = RustLib._();

  RustLib._();

  /// Initialize flutter_rust_bridge
  static Future<void> init({
    RustLibApi? api,
    BaseHandler? handler,
    ExternalLibrary? externalLibrary,
  }) async {
    await instance.initImpl(
      api: api,
      handler: handler,
      externalLibrary: externalLibrary,
    );
  }

  /// Initialize flutter_rust_bridge in mock mode.
  /// No libraries for FFI are loaded.
  static void initMock({
    required RustLibApi api,
  }) {
    instance.initMockImpl(
      api: api,
    );
  }

  /// Dispose flutter_rust_bridge
  ///
  /// The call to this function is optional, since flutter_rust_bridge (and everything else)
  /// is automatically disposed when the app stops.
  static void dispose() => instance.disposeImpl();

  @override
  ApiImplConstructor<RustLibApiImpl, RustLibWire> get apiImplConstructor =>
      RustLibApiImpl.new;

  @override
  WireConstructor<RustLibWire> get wireConstructor =>
      RustLibWire.fromExternalLibrary;

  @override
  Future<void> executeRustInitializers() async {}

  @override
  ExternalLibraryLoaderConfig get defaultExternalLibraryLoaderConfig =>
      kDefaultExternalLibraryLoaderConfig;

  @override
  String get codegenVersion => '2.5.0';

  @override
  int get rustContentHash => 160229826;

  static const kDefaultExternalLibraryLoaderConfig =
      ExternalLibraryLoaderConfig(
    stem: 'rust_uplus',
    ioDirectory: '../../rust_uplus/target/release/',
    webPrefix: 'pkg/',
  );
}

abstract class RustLibApi extends BaseApi {
  RustConsumer crateFeaturesFlutterDartRustConsumerNew(
      {required String uniqueId,
      required FutureOr<void> Function(Uint8List) consumer});

  RustFunction crateFeaturesFlutterDartRustFunctionNew(
      {required String uniqueId,
      required FutureOr<Uint8List> Function(Uint8List) function});

  RustSupplier crateFeaturesFlutterDartRustSupplierNew(
      {required String uniqueId,
      required FutureOr<Uint8List> Function() supplier});

  Future<Uint8List> crateFeaturesFlutterDartFlatCrossPlatformAsdAsr(
      {required String lib, required Map<String, String> params});

  Future<Uint8List> crateFeaturesFlutterDartFlatCrossPlatformAsdSr(
      {required String lib, required Map<String, String> params});

  void crateFeaturesFlutterDartFlatCrossPlatformConsumer(
      {required String lib,
      required Map<String, String> params,
      required RustConsumer consumer});

  Uint8List crateFeaturesFlutterDartFlatCrossPlatformConsumerData(
      {required String lib,
      required Map<String, String> params,
      required RustConsumer consumer});

  void crateFeaturesFlutterDartFlatCrossPlatformFunction(
      {required String lib,
      required Map<String, String> params,
      required RustFunction function});

  Uint8List crateFeaturesFlutterDartFlatCrossPlatformSdSr(
      {required String lib, required Map<String, String> params});

  void crateFeaturesFlutterDartFlatCrossPlatformSupplier(
      {required String lib,
      required Map<String, String> params,
      required RustSupplier supplier});

  RustArcIncrementStrongCountFnType
      get rust_arc_increment_strong_count_RustConsumer;

  RustArcDecrementStrongCountFnType
      get rust_arc_decrement_strong_count_RustConsumer;

  CrossPlatformFinalizerArg get rust_arc_decrement_strong_count_RustConsumerPtr;

  RustArcIncrementStrongCountFnType
      get rust_arc_increment_strong_count_RustFunction;

  RustArcDecrementStrongCountFnType
      get rust_arc_decrement_strong_count_RustFunction;

  CrossPlatformFinalizerArg get rust_arc_decrement_strong_count_RustFunctionPtr;

  RustArcIncrementStrongCountFnType
      get rust_arc_increment_strong_count_RustSupplier;

  RustArcDecrementStrongCountFnType
      get rust_arc_decrement_strong_count_RustSupplier;

  CrossPlatformFinalizerArg get rust_arc_decrement_strong_count_RustSupplierPtr;
}

class RustLibApiImpl extends RustLibApiImplPlatform implements RustLibApi {
  RustLibApiImpl({
    required super.handler,
    required super.wire,
    required super.generalizedFrbRustBinding,
    required super.portManager,
  });

  @override
  RustConsumer crateFeaturesFlutterDartRustConsumerNew(
      {required String uniqueId,
      required FutureOr<void> Function(Uint8List) consumer}) {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_String(uniqueId, serializer);
        sse_encode_DartFn_Inputs_list_prim_u_8_strict_Output_unit_AnyhowException(
            consumer, serializer);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 1)!;
      },
      codec: SseCodec(
        decodeSuccessData:
            sse_decode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerRustConsumer,
        decodeErrorData: null,
      ),
      constMeta: kCrateFeaturesFlutterDartRustConsumerNewConstMeta,
      argValues: [uniqueId, consumer],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateFeaturesFlutterDartRustConsumerNewConstMeta =>
      const TaskConstMeta(
        debugName: "RustConsumer_new",
        argNames: ["uniqueId", "consumer"],
      );

  @override
  RustFunction crateFeaturesFlutterDartRustFunctionNew(
      {required String uniqueId,
      required FutureOr<Uint8List> Function(Uint8List) function}) {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_String(uniqueId, serializer);
        sse_encode_DartFn_Inputs_list_prim_u_8_strict_Output_list_prim_u_8_strict_AnyhowException(
            function, serializer);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 2)!;
      },
      codec: SseCodec(
        decodeSuccessData:
            sse_decode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerRustFunction,
        decodeErrorData: null,
      ),
      constMeta: kCrateFeaturesFlutterDartRustFunctionNewConstMeta,
      argValues: [uniqueId, function],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateFeaturesFlutterDartRustFunctionNewConstMeta =>
      const TaskConstMeta(
        debugName: "RustFunction_new",
        argNames: ["uniqueId", "function"],
      );

  @override
  RustSupplier crateFeaturesFlutterDartRustSupplierNew(
      {required String uniqueId,
      required FutureOr<Uint8List> Function() supplier}) {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_String(uniqueId, serializer);
        sse_encode_DartFn_Inputs__Output_list_prim_u_8_strict_AnyhowException(
            supplier, serializer);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 3)!;
      },
      codec: SseCodec(
        decodeSuccessData:
            sse_decode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerRustSupplier,
        decodeErrorData: null,
      ),
      constMeta: kCrateFeaturesFlutterDartRustSupplierNewConstMeta,
      argValues: [uniqueId, supplier],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateFeaturesFlutterDartRustSupplierNewConstMeta =>
      const TaskConstMeta(
        debugName: "RustSupplier_new",
        argNames: ["uniqueId", "supplier"],
      );

  @override
  Future<Uint8List> crateFeaturesFlutterDartFlatCrossPlatformAsdAsr(
      {required String lib, required Map<String, String> params}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_String(lib, serializer);
        sse_encode_Map_String_String(params, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 4, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_list_prim_u_8_strict,
        decodeErrorData: null,
      ),
      constMeta: kCrateFeaturesFlutterDartFlatCrossPlatformAsdAsrConstMeta,
      argValues: [lib, params],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateFeaturesFlutterDartFlatCrossPlatformAsdAsrConstMeta =>
      const TaskConstMeta(
        debugName: "flat_cross_platform_asd_asr",
        argNames: ["lib", "params"],
      );

  @override
  Future<Uint8List> crateFeaturesFlutterDartFlatCrossPlatformAsdSr(
      {required String lib, required Map<String, String> params}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_String(lib, serializer);
        sse_encode_Map_String_String(params, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 5, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_list_prim_u_8_strict,
        decodeErrorData: null,
      ),
      constMeta: kCrateFeaturesFlutterDartFlatCrossPlatformAsdSrConstMeta,
      argValues: [lib, params],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateFeaturesFlutterDartFlatCrossPlatformAsdSrConstMeta =>
      const TaskConstMeta(
        debugName: "flat_cross_platform_asd_sr",
        argNames: ["lib", "params"],
      );

  @override
  void crateFeaturesFlutterDartFlatCrossPlatformConsumer(
      {required String lib,
      required Map<String, String> params,
      required RustConsumer consumer}) {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_String(lib, serializer);
        sse_encode_Map_String_String(params, serializer);
        sse_encode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerRustConsumer(
            consumer, serializer);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 6)!;
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_unit,
        decodeErrorData: null,
      ),
      constMeta: kCrateFeaturesFlutterDartFlatCrossPlatformConsumerConstMeta,
      argValues: [lib, params, consumer],
      apiImpl: this,
    ));
  }

  TaskConstMeta
      get kCrateFeaturesFlutterDartFlatCrossPlatformConsumerConstMeta =>
          const TaskConstMeta(
            debugName: "flat_cross_platform_consumer",
            argNames: ["lib", "params", "consumer"],
          );

  @override
  Uint8List crateFeaturesFlutterDartFlatCrossPlatformConsumerData(
      {required String lib,
      required Map<String, String> params,
      required RustConsumer consumer}) {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_String(lib, serializer);
        sse_encode_Map_String_String(params, serializer);
        sse_encode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerRustConsumer(
            consumer, serializer);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 7)!;
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_list_prim_u_8_strict,
        decodeErrorData: null,
      ),
      constMeta:
          kCrateFeaturesFlutterDartFlatCrossPlatformConsumerDataConstMeta,
      argValues: [lib, params, consumer],
      apiImpl: this,
    ));
  }

  TaskConstMeta
      get kCrateFeaturesFlutterDartFlatCrossPlatformConsumerDataConstMeta =>
          const TaskConstMeta(
            debugName: "flat_cross_platform_consumer_data",
            argNames: ["lib", "params", "consumer"],
          );

  @override
  void crateFeaturesFlutterDartFlatCrossPlatformFunction(
      {required String lib,
      required Map<String, String> params,
      required RustFunction function}) {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_String(lib, serializer);
        sse_encode_Map_String_String(params, serializer);
        sse_encode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerRustFunction(
            function, serializer);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 8)!;
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_unit,
        decodeErrorData: null,
      ),
      constMeta: kCrateFeaturesFlutterDartFlatCrossPlatformFunctionConstMeta,
      argValues: [lib, params, function],
      apiImpl: this,
    ));
  }

  TaskConstMeta
      get kCrateFeaturesFlutterDartFlatCrossPlatformFunctionConstMeta =>
          const TaskConstMeta(
            debugName: "flat_cross_platform_function",
            argNames: ["lib", "params", "function"],
          );

  @override
  Uint8List crateFeaturesFlutterDartFlatCrossPlatformSdSr(
      {required String lib, required Map<String, String> params}) {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_String(lib, serializer);
        sse_encode_Map_String_String(params, serializer);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 9)!;
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_list_prim_u_8_strict,
        decodeErrorData: null,
      ),
      constMeta: kCrateFeaturesFlutterDartFlatCrossPlatformSdSrConstMeta,
      argValues: [lib, params],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateFeaturesFlutterDartFlatCrossPlatformSdSrConstMeta =>
      const TaskConstMeta(
        debugName: "flat_cross_platform_sd_sr",
        argNames: ["lib", "params"],
      );

  @override
  void crateFeaturesFlutterDartFlatCrossPlatformSupplier(
      {required String lib,
      required Map<String, String> params,
      required RustSupplier supplier}) {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_String(lib, serializer);
        sse_encode_Map_String_String(params, serializer);
        sse_encode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerRustSupplier(
            supplier, serializer);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 10)!;
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_unit,
        decodeErrorData: null,
      ),
      constMeta: kCrateFeaturesFlutterDartFlatCrossPlatformSupplierConstMeta,
      argValues: [lib, params, supplier],
      apiImpl: this,
    ));
  }

  TaskConstMeta
      get kCrateFeaturesFlutterDartFlatCrossPlatformSupplierConstMeta =>
          const TaskConstMeta(
            debugName: "flat_cross_platform_supplier",
            argNames: ["lib", "params", "supplier"],
          );

  Future<void> Function(
    int,
  ) encode_DartFn_Inputs__Output_list_prim_u_8_strict_AnyhowException(
      FutureOr<Uint8List> Function() raw) {
    return (
      callId,
    ) async {
      Box<Uint8List>? rawOutput;
      Box<AnyhowException>? rawError;
      try {
        rawOutput = Box(await raw());
      } catch (e, s) {
        rawError = Box(AnyhowException("$e\n\n$s"));
      }

      final serializer = SseSerializer(generalizedFrbRustBinding);
      assert((rawOutput != null) ^ (rawError != null));
      if (rawOutput != null) {
        serializer.buffer.putUint8(0);
        sse_encode_list_prim_u_8_strict(rawOutput.value, serializer);
      } else {
        serializer.buffer.putUint8(1);
        sse_encode_AnyhowException(rawError!.value, serializer);
      }
      final output = serializer.intoRaw();

      generalizedFrbRustBinding.dartFnDeliverOutput(
          callId: callId,
          ptr: output.ptr,
          rustVecLen: output.rustVecLen,
          dataLen: output.dataLen);
    };
  }

  Future<void> Function(int, dynamic)
      encode_DartFn_Inputs_list_prim_u_8_strict_Output_list_prim_u_8_strict_AnyhowException(
          FutureOr<Uint8List> Function(Uint8List) raw) {
    return (callId, rawArg0) async {
      final arg0 = dco_decode_list_prim_u_8_strict(rawArg0);

      Box<Uint8List>? rawOutput;
      Box<AnyhowException>? rawError;
      try {
        rawOutput = Box(await raw(arg0));
      } catch (e, s) {
        rawError = Box(AnyhowException("$e\n\n$s"));
      }

      final serializer = SseSerializer(generalizedFrbRustBinding);
      assert((rawOutput != null) ^ (rawError != null));
      if (rawOutput != null) {
        serializer.buffer.putUint8(0);
        sse_encode_list_prim_u_8_strict(rawOutput.value, serializer);
      } else {
        serializer.buffer.putUint8(1);
        sse_encode_AnyhowException(rawError!.value, serializer);
      }
      final output = serializer.intoRaw();

      generalizedFrbRustBinding.dartFnDeliverOutput(
          callId: callId,
          ptr: output.ptr,
          rustVecLen: output.rustVecLen,
          dataLen: output.dataLen);
    };
  }

  Future<void> Function(int, dynamic)
      encode_DartFn_Inputs_list_prim_u_8_strict_Output_unit_AnyhowException(
          FutureOr<void> Function(Uint8List) raw) {
    return (callId, rawArg0) async {
      final arg0 = dco_decode_list_prim_u_8_strict(rawArg0);

      Box<void>? rawOutput;
      Box<AnyhowException>? rawError;
      try {
        rawOutput = Box(await raw(arg0));
      } catch (e, s) {
        rawError = Box(AnyhowException("$e\n\n$s"));
      }

      final serializer = SseSerializer(generalizedFrbRustBinding);
      assert((rawOutput != null) ^ (rawError != null));
      if (rawOutput != null) {
        serializer.buffer.putUint8(0);
        sse_encode_unit(rawOutput.value, serializer);
      } else {
        serializer.buffer.putUint8(1);
        sse_encode_AnyhowException(rawError!.value, serializer);
      }
      final output = serializer.intoRaw();

      generalizedFrbRustBinding.dartFnDeliverOutput(
          callId: callId,
          ptr: output.ptr,
          rustVecLen: output.rustVecLen,
          dataLen: output.dataLen);
    };
  }

  RustArcIncrementStrongCountFnType
      get rust_arc_increment_strong_count_RustConsumer => wire
          .rust_arc_increment_strong_count_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerRustConsumer;

  RustArcDecrementStrongCountFnType
      get rust_arc_decrement_strong_count_RustConsumer => wire
          .rust_arc_decrement_strong_count_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerRustConsumer;

  RustArcIncrementStrongCountFnType
      get rust_arc_increment_strong_count_RustFunction => wire
          .rust_arc_increment_strong_count_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerRustFunction;

  RustArcDecrementStrongCountFnType
      get rust_arc_decrement_strong_count_RustFunction => wire
          .rust_arc_decrement_strong_count_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerRustFunction;

  RustArcIncrementStrongCountFnType
      get rust_arc_increment_strong_count_RustSupplier => wire
          .rust_arc_increment_strong_count_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerRustSupplier;

  RustArcDecrementStrongCountFnType
      get rust_arc_decrement_strong_count_RustSupplier => wire
          .rust_arc_decrement_strong_count_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerRustSupplier;

  @protected
  AnyhowException dco_decode_AnyhowException(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return AnyhowException(raw as String);
  }

  @protected
  RustConsumer
      dco_decode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerRustConsumer(
          dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return RustConsumerImpl.frbInternalDcoDecode(raw as List<dynamic>);
  }

  @protected
  RustFunction
      dco_decode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerRustFunction(
          dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return RustFunctionImpl.frbInternalDcoDecode(raw as List<dynamic>);
  }

  @protected
  RustSupplier
      dco_decode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerRustSupplier(
          dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return RustSupplierImpl.frbInternalDcoDecode(raw as List<dynamic>);
  }

  @protected
  FutureOr<Uint8List> Function()
      dco_decode_DartFn_Inputs__Output_list_prim_u_8_strict_AnyhowException(
          dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    throw UnimplementedError('');
  }

  @protected
  FutureOr<Uint8List> Function(Uint8List)
      dco_decode_DartFn_Inputs_list_prim_u_8_strict_Output_list_prim_u_8_strict_AnyhowException(
          dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    throw UnimplementedError('');
  }

  @protected
  FutureOr<void> Function(Uint8List)
      dco_decode_DartFn_Inputs_list_prim_u_8_strict_Output_unit_AnyhowException(
          dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    throw UnimplementedError('');
  }

  @protected
  Object dco_decode_DartOpaque(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return decodeDartOpaque(raw, generalizedFrbRustBinding);
  }

  @protected
  Map<String, String> dco_decode_Map_String_String(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return Map.fromEntries(dco_decode_list_record_string_string(raw)
        .map((e) => MapEntry(e.$1, e.$2)));
  }

  @protected
  RustConsumer
      dco_decode_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerRustConsumer(
          dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return RustConsumerImpl.frbInternalDcoDecode(raw as List<dynamic>);
  }

  @protected
  RustFunction
      dco_decode_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerRustFunction(
          dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return RustFunctionImpl.frbInternalDcoDecode(raw as List<dynamic>);
  }

  @protected
  RustSupplier
      dco_decode_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerRustSupplier(
          dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return RustSupplierImpl.frbInternalDcoDecode(raw as List<dynamic>);
  }

  @protected
  String dco_decode_String(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return raw as String;
  }

  @protected
  PlatformInt64 dco_decode_isize(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return dcoDecodeI64(raw);
  }

  @protected
  Uint8List dco_decode_list_prim_u_8_strict(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return raw as Uint8List;
  }

  @protected
  List<(String, String)> dco_decode_list_record_string_string(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return (raw as List<dynamic>).map(dco_decode_record_string_string).toList();
  }

  @protected
  (String, String) dco_decode_record_string_string(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    final arr = raw as List<dynamic>;
    if (arr.length != 2) {
      throw Exception('Expected 2 elements, got ${arr.length}');
    }
    return (
      dco_decode_String(arr[0]),
      dco_decode_String(arr[1]),
    );
  }

  @protected
  int dco_decode_u_8(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return raw as int;
  }

  @protected
  void dco_decode_unit(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return;
  }

  @protected
  BigInt dco_decode_usize(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return dcoDecodeU64(raw);
  }

  @protected
  AnyhowException sse_decode_AnyhowException(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var inner = sse_decode_String(deserializer);
    return AnyhowException(inner);
  }

  @protected
  RustConsumer
      sse_decode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerRustConsumer(
          SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return RustConsumerImpl.frbInternalSseDecode(
        sse_decode_usize(deserializer), sse_decode_i_32(deserializer));
  }

  @protected
  RustFunction
      sse_decode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerRustFunction(
          SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return RustFunctionImpl.frbInternalSseDecode(
        sse_decode_usize(deserializer), sse_decode_i_32(deserializer));
  }

  @protected
  RustSupplier
      sse_decode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerRustSupplier(
          SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return RustSupplierImpl.frbInternalSseDecode(
        sse_decode_usize(deserializer), sse_decode_i_32(deserializer));
  }

  @protected
  Object sse_decode_DartOpaque(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var inner = sse_decode_isize(deserializer);
    return decodeDartOpaque(inner, generalizedFrbRustBinding);
  }

  @protected
  Map<String, String> sse_decode_Map_String_String(
      SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var inner = sse_decode_list_record_string_string(deserializer);
    return Map.fromEntries(inner.map((e) => MapEntry(e.$1, e.$2)));
  }

  @protected
  RustConsumer
      sse_decode_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerRustConsumer(
          SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return RustConsumerImpl.frbInternalSseDecode(
        sse_decode_usize(deserializer), sse_decode_i_32(deserializer));
  }

  @protected
  RustFunction
      sse_decode_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerRustFunction(
          SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return RustFunctionImpl.frbInternalSseDecode(
        sse_decode_usize(deserializer), sse_decode_i_32(deserializer));
  }

  @protected
  RustSupplier
      sse_decode_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerRustSupplier(
          SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return RustSupplierImpl.frbInternalSseDecode(
        sse_decode_usize(deserializer), sse_decode_i_32(deserializer));
  }

  @protected
  String sse_decode_String(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var inner = sse_decode_list_prim_u_8_strict(deserializer);
    return utf8.decoder.convert(inner);
  }

  @protected
  PlatformInt64 sse_decode_isize(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return deserializer.buffer.getPlatformInt64();
  }

  @protected
  Uint8List sse_decode_list_prim_u_8_strict(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var len_ = sse_decode_i_32(deserializer);
    return deserializer.buffer.getUint8List(len_);
  }

  @protected
  List<(String, String)> sse_decode_list_record_string_string(
      SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs

    var len_ = sse_decode_i_32(deserializer);
    var ans_ = <(String, String)>[];
    for (var idx_ = 0; idx_ < len_; ++idx_) {
      ans_.add(sse_decode_record_string_string(deserializer));
    }
    return ans_;
  }

  @protected
  (String, String) sse_decode_record_string_string(
      SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var var_field0 = sse_decode_String(deserializer);
    var var_field1 = sse_decode_String(deserializer);
    return (var_field0, var_field1);
  }

  @protected
  int sse_decode_u_8(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return deserializer.buffer.getUint8();
  }

  @protected
  void sse_decode_unit(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
  }

  @protected
  BigInt sse_decode_usize(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return deserializer.buffer.getBigUint64();
  }

  @protected
  int sse_decode_i_32(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return deserializer.buffer.getInt32();
  }

  @protected
  bool sse_decode_bool(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return deserializer.buffer.getUint8() != 0;
  }

  @protected
  void sse_encode_AnyhowException(
      AnyhowException self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_String(self.message, serializer);
  }

  @protected
  void
      sse_encode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerRustConsumer(
          RustConsumer self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_usize(
        (self as RustConsumerImpl).frbInternalSseEncode(move: true),
        serializer);
  }

  @protected
  void
      sse_encode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerRustFunction(
          RustFunction self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_usize(
        (self as RustFunctionImpl).frbInternalSseEncode(move: true),
        serializer);
  }

  @protected
  void
      sse_encode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerRustSupplier(
          RustSupplier self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_usize(
        (self as RustSupplierImpl).frbInternalSseEncode(move: true),
        serializer);
  }

  @protected
  void sse_encode_DartFn_Inputs__Output_list_prim_u_8_strict_AnyhowException(
      FutureOr<Uint8List> Function() self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_DartOpaque(
        encode_DartFn_Inputs__Output_list_prim_u_8_strict_AnyhowException(self),
        serializer);
  }

  @protected
  void
      sse_encode_DartFn_Inputs_list_prim_u_8_strict_Output_list_prim_u_8_strict_AnyhowException(
          FutureOr<Uint8List> Function(Uint8List) self,
          SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_DartOpaque(
        encode_DartFn_Inputs_list_prim_u_8_strict_Output_list_prim_u_8_strict_AnyhowException(
            self),
        serializer);
  }

  @protected
  void
      sse_encode_DartFn_Inputs_list_prim_u_8_strict_Output_unit_AnyhowException(
          FutureOr<void> Function(Uint8List) self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_DartOpaque(
        encode_DartFn_Inputs_list_prim_u_8_strict_Output_unit_AnyhowException(
            self),
        serializer);
  }

  @protected
  void sse_encode_DartOpaque(Object self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_isize(
        PlatformPointerUtil.ptrToPlatformInt64(encodeDartOpaque(
            self, portManager.dartHandlerPort, generalizedFrbRustBinding)),
        serializer);
  }

  @protected
  void sse_encode_Map_String_String(
      Map<String, String> self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_list_record_string_string(
        self.entries.map((e) => (e.key, e.value)).toList(), serializer);
  }

  @protected
  void
      sse_encode_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerRustConsumer(
          RustConsumer self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_usize(
        (self as RustConsumerImpl).frbInternalSseEncode(move: null),
        serializer);
  }

  @protected
  void
      sse_encode_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerRustFunction(
          RustFunction self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_usize(
        (self as RustFunctionImpl).frbInternalSseEncode(move: null),
        serializer);
  }

  @protected
  void
      sse_encode_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerRustSupplier(
          RustSupplier self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_usize(
        (self as RustSupplierImpl).frbInternalSseEncode(move: null),
        serializer);
  }

  @protected
  void sse_encode_String(String self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_list_prim_u_8_strict(utf8.encoder.convert(self), serializer);
  }

  @protected
  void sse_encode_isize(PlatformInt64 self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    serializer.buffer.putPlatformInt64(self);
  }

  @protected
  void sse_encode_list_prim_u_8_strict(
      Uint8List self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_i_32(self.length, serializer);
    serializer.buffer.putUint8List(self);
  }

  @protected
  void sse_encode_list_record_string_string(
      List<(String, String)> self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_i_32(self.length, serializer);
    for (final item in self) {
      sse_encode_record_string_string(item, serializer);
    }
  }

  @protected
  void sse_encode_record_string_string(
      (String, String) self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_String(self.$1, serializer);
    sse_encode_String(self.$2, serializer);
  }

  @protected
  void sse_encode_u_8(int self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    serializer.buffer.putUint8(self);
  }

  @protected
  void sse_encode_unit(void self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
  }

  @protected
  void sse_encode_usize(BigInt self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    serializer.buffer.putBigUint64(self);
  }

  @protected
  void sse_encode_i_32(int self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    serializer.buffer.putInt32(self);
  }

  @protected
  void sse_encode_bool(bool self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    serializer.buffer.putUint8(self ? 1 : 0);
  }
}

@sealed
class RustConsumerImpl extends RustOpaque implements RustConsumer {
  // Not to be used by end users
  RustConsumerImpl.frbInternalDcoDecode(List<dynamic> wire)
      : super.frbInternalDcoDecode(wire, _kStaticData);

  // Not to be used by end users
  RustConsumerImpl.frbInternalSseDecode(BigInt ptr, int externalSizeOnNative)
      : super.frbInternalSseDecode(ptr, externalSizeOnNative, _kStaticData);

  static final _kStaticData = RustArcStaticData(
    rustArcIncrementStrongCount:
        RustLib.instance.api.rust_arc_increment_strong_count_RustConsumer,
    rustArcDecrementStrongCount:
        RustLib.instance.api.rust_arc_decrement_strong_count_RustConsumer,
    rustArcDecrementStrongCountPtr:
        RustLib.instance.api.rust_arc_decrement_strong_count_RustConsumerPtr,
  );
}

@sealed
class RustFunctionImpl extends RustOpaque implements RustFunction {
  // Not to be used by end users
  RustFunctionImpl.frbInternalDcoDecode(List<dynamic> wire)
      : super.frbInternalDcoDecode(wire, _kStaticData);

  // Not to be used by end users
  RustFunctionImpl.frbInternalSseDecode(BigInt ptr, int externalSizeOnNative)
      : super.frbInternalSseDecode(ptr, externalSizeOnNative, _kStaticData);

  static final _kStaticData = RustArcStaticData(
    rustArcIncrementStrongCount:
        RustLib.instance.api.rust_arc_increment_strong_count_RustFunction,
    rustArcDecrementStrongCount:
        RustLib.instance.api.rust_arc_decrement_strong_count_RustFunction,
    rustArcDecrementStrongCountPtr:
        RustLib.instance.api.rust_arc_decrement_strong_count_RustFunctionPtr,
  );
}

@sealed
class RustSupplierImpl extends RustOpaque implements RustSupplier {
  // Not to be used by end users
  RustSupplierImpl.frbInternalDcoDecode(List<dynamic> wire)
      : super.frbInternalDcoDecode(wire, _kStaticData);

  // Not to be used by end users
  RustSupplierImpl.frbInternalSseDecode(BigInt ptr, int externalSizeOnNative)
      : super.frbInternalSseDecode(ptr, externalSizeOnNative, _kStaticData);

  static final _kStaticData = RustArcStaticData(
    rustArcIncrementStrongCount:
        RustLib.instance.api.rust_arc_increment_strong_count_RustSupplier,
    rustArcDecrementStrongCount:
        RustLib.instance.api.rust_arc_decrement_strong_count_RustSupplier,
    rustArcDecrementStrongCountPtr:
        RustLib.instance.api.rust_arc_decrement_strong_count_RustSupplierPtr,
  );
}
