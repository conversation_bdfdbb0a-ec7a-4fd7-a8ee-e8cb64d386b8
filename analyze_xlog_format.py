#!/usr/bin/env python3

import sys
import os
import struct

def analyze_xlog_data(data):
    """分析xlog解码后的数据格式"""
    print(f"🔍 Analyzing {len(data)} bytes of xlog data...")
    
    # 完整的十六进制dump
    print("\n📋 Complete hex dump:")
    for i in range(0, len(data), 16):
        hex_part = ' '.join(f'{b:02x}' for b in data[i:i+16])
        ascii_part = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in data[i:i+16])
        print(f"{i:04x}: {hex_part:<48} |{ascii_part}|")
    
    # 查找模式
    print(f"\n🎯 Pattern analysis:")
    
    # 查找0xff 0xff模式
    ff_positions = []
    for i in range(len(data) - 1):
        if data[i] == 0xff and data[i+1] == 0xff:
            ff_positions.append(i)
    print(f"0xff 0xff patterns at: {ff_positions}")
    
    # 查找0x00 0x00 0x00 0x00模式
    null_positions = []
    for i in range(len(data) - 3):
        if data[i:i+4] == b'\x00\x00\x00\x00':
            null_positions.append(i)
    print(f"0x00 0x00 0x00 0x00 patterns at: {null_positions}")
    
    # 尝试按0xff 0xff分割
    if ff_positions:
        print(f"\n📦 Splitting by 0xff 0xff patterns:")
        segments = []
        start = 0
        
        for pos in ff_positions:
            if start < pos:
                segment = data[start:pos]
                segments.append(segment)
                print(f"Segment {len(segments)}: bytes {start}-{pos-1} ({len(segment)} bytes)")
                print(f"  Hex: {' '.join(f'{b:02x}' for b in segment[:16])}{'...' if len(segment) > 16 else ''}")
            start = pos + 2  # 跳过0xff 0xff
        
        # 最后一个段
        if start < len(data):
            segment = data[start:]
            segments.append(segment)
            print(f"Segment {len(segments)}: bytes {start}-{len(data)-1} ({len(segment)} bytes)")
            print(f"  Hex: {' '.join(f'{b:02x}' for b in segment[:16])}{'...' if len(segment) > 16 else ''}")
        
        return segments
    
    return [data]

def try_parse_segment_as_text(segment, segment_id):
    """尝试将段解析为文本"""
    print(f"\n🔤 Trying to parse segment {segment_id} as text...")
    
    for encoding in ['utf-8', 'latin-1', 'ascii', 'gbk', 'utf-16', 'utf-32']:
        try:
            text = segment.decode(encoding)
            # 检查是否包含可打印字符
            printable_ratio = sum(1 for c in text if c.isprintable() or c.isspace()) / len(text)
            if printable_ratio > 0.7:  # 至少70%是可打印字符
                print(f"✅ Decoded as {encoding} (printable ratio: {printable_ratio:.2f})")
                print(f"📝 Content: {repr(text[:100])}")
                return text, encoding
        except (UnicodeDecodeError, UnicodeError):
            continue
    
    print(f"❌ No suitable text encoding found")
    return None, None

def try_parse_segment_as_structured(segment, segment_id):
    """尝试将段解析为结构化数据"""
    print(f"\n🏗️  Trying to parse segment {segment_id} as structured data...")
    
    if len(segment) < 4:
        print(f"❌ Segment too short ({len(segment)} bytes)")
        return None
    
    # 尝试读取长度前缀
    try:
        # 尝试小端序32位整数
        length_le = struct.unpack('<I', segment[:4])[0]
        print(f"📏 Possible length (little-endian): {length_le}")
        
        # 尝试大端序32位整数
        length_be = struct.unpack('>I', segment[:4])[0]
        print(f"📏 Possible length (big-endian): {length_be}")
        
        # 检查长度是否合理
        if 4 <= length_le <= len(segment) - 4:
            print(f"✅ Little-endian length {length_le} seems reasonable")
            payload = segment[4:4+length_le]
            print(f"📦 Payload: {' '.join(f'{b:02x}' for b in payload[:16])}{'...' if len(payload) > 16 else ''}")
            return payload, 'length_prefixed_le'
        
        if 4 <= length_be <= len(segment) - 4:
            print(f"✅ Big-endian length {length_be} seems reasonable")
            payload = segment[4:4+length_be]
            print(f"📦 Payload: {' '.join(f'{b:02x}' for b in payload[:16])}{'...' if len(payload) > 16 else ''}")
            return payload, 'length_prefixed_be'
            
    except struct.error:
        pass
    
    print(f"❌ No structured format detected")
    return None, None

def main():
    if len(sys.argv) != 2:
        print("Usage: python3 analyze_xlog_format.py <decoded_log_file>")
        print("Example: python3 analyze_xlog_format.py /Users/<USER>/Downloads/uplog_20250620.xlog.log")
        sys.exit(1)
    
    log_file = sys.argv[1]
    
    if not os.path.exists(log_file):
        print(f"❌ File not found: {log_file}")
        sys.exit(1)
    
    print(f"🔧 Analyzing file: {log_file}")
    
    with open(log_file, 'rb') as f:
        data = f.read()
    
    # 分析数据格式
    segments = analyze_xlog_data(data)
    
    # 分析每个段
    results = []
    for i, segment in enumerate(segments, 1):
        print(f"\n{'='*60}")
        print(f"🔍 Analyzing segment {i} ({len(segment)} bytes)")
        
        # 尝试解析为文本
        text, encoding = try_parse_segment_as_text(segment, i)
        if text:
            results.append(('text', text, encoding))
            continue
        
        # 尝试解析为结构化数据
        payload, format_type = try_parse_segment_as_structured(segment, i)
        if payload:
            # 递归分析payload
            print(f"🔄 Recursively analyzing payload...")
            text, encoding = try_parse_segment_as_text(payload, f"{i}.payload")
            if text:
                results.append(('structured_text', text, encoding))
            else:
                results.append(('structured_binary', payload, format_type))
        else:
            results.append(('binary', segment, 'unknown'))
    
    # 输出结果
    print(f"\n{'='*80}")
    print(f"📊 ANALYSIS RESULTS")
    print(f"{'='*80}")
    
    output_file = log_file + ".analysis.txt"
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("XLog Data Analysis Results\n")
        f.write("=" * 50 + "\n\n")
        
        for i, (data_type, content, info) in enumerate(results, 1):
            print(f"\n📝 Segment {i}: {data_type} ({info})")
            f.write(f"Segment {i}: {data_type} ({info})\n")
            f.write("-" * 30 + "\n")
            
            if data_type in ['text', 'structured_text']:
                print(f"📄 Content: {repr(content[:200])}")
                f.write(f"Content: {content}\n")
            else:
                print(f"📦 Binary data: {len(content)} bytes")
                f.write(f"Binary data: {len(content)} bytes\n")
                f.write(f"Hex: {' '.join(f'{b:02x}' for b in content[:32])}\n")
            
            f.write("\n")
    
    print(f"\n💾 Analysis results saved to: {output_file}")

if __name__ == "__main__":
    main()
