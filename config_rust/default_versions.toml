[workspace]
members = [
    "logic_engine_rust/rust_logicEngine",
    "request_rust/request_rust",
    "resource_rust/rust_resource",
    "storage_rust/rust_storage",
    "task_manager_rust/task_manager_rust",
    "updevice_rust/rust_updevice",
    "rust_uplus/rust_uplus",
    "uplus_main_rust/rust_main_uplus",
    "userdomain_rust/rust_userdomain",
    "security_rust/rust_security",
    "usdk_rust/rust_usdk",
    "logger/rust_logger"
]
resolver = "2"


[workspace.dependencies]
flutter_rust_bridge = "=2.5.0"
security-framework = "=2.10.0"
flatbuffers = "=24.3.25"
jni = "=0.21.1"
android_logger = "=0.14.1"
napi-ohos = "=1.0.3"
napi-derive-ohos = "=1.0.3"
napi-derive-backend-ohos = "=1.0.3"
napi-sys-ohos = "=1.0.3"
serde = "=1.0.217"
serde_json = "=1.0.138"
log = "=0.4.25"
parking_lot = "=0.12.3"
reqwest = "=0.12.12"
getset = "=0.1.4"
sha2 = "=0.10.8"
once_cell = "=1.20.2"
md5 = "=0.7.0"
uuid = "=1.13.1"
thiserror = "=2.0.11"
async-trait = "=0.1.86"
async-std = "=1.13.0"
diesel = "=2.2.6"
r2d2 = "=0.8.10"
zip = "=2.3.0"
tokio = "=1.43.0"
chrono = "=0.4.39"
futures = "=0.3.31"
dashmap = "=6.1.0"
indextree = "=4.7.3"
rust_decimal = "=1.36.0"
hashlink = "=0.10.0"
rand = "=0.9.0"
bigdecimal = "=0.4.7"
hex = "=0.4.3"
regex = "=1.11.1"
async-recursion = "=1.1.1"
libc = "=0.2.169"
base64 = "=0.22.1"
openssl = "=0.10.70"
backtrace = "=0.3.74"
derive_builder = "=0.20.2"

# Build
cc = "=1.2.12"
napi-build-ohos = "=1.0.3"

# Unit Test
env_logger = "=0.11.6"
mockall = "=0.13.1"
mry = "=0.10.0"
cucumber = "=0.21.1"
