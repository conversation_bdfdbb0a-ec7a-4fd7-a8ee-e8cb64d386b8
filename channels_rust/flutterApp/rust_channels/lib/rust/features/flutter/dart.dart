// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.5.0.

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import '../../frb_generated.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';

// These function are ignored because they are on traits that is not defined in current crate (put an empty `#[frb]` on it to unignore): `accept`, `apply`, `get`, `unique_id`, `unique_id`, `unique_id`

/// FRB 对于同步和异步支持的说明
/// https://cjycode.com/flutter_rust_bridge/guides/concurrency/overview
/// Sync Dart + Async Rust 是不合理的
/// * 推荐使用：
/// * Async Dart + Sync Rust
/// * Async Dart + Async Rust
/// * Sync Dart + Sync Rust
///
/// `ZeroCopyBuffer<T>` is no longer needed, since zero-copy is automatically utilized,
/// just directly use `T` instead.
/// Sync Dart + Sync Rust
Uint8List flatCrossPlatformSdSr(
        {required String lib, required Map<String, String> params}) =>
    RustLib.instance.api.crateFeaturesFlutterDartFlatCrossPlatformSdSr(
        lib: lib, params: params);

/// Async Dart + Sync Rust
Future<Uint8List> flatCrossPlatformAsdSr(
        {required String lib, required Map<String, String> params}) =>
    RustLib.instance.api.crateFeaturesFlutterDartFlatCrossPlatformAsdSr(
        lib: lib, params: params);

/// Async Dart + Async Rust
Future<Uint8List> flatCrossPlatformAsdAsr(
        {required String lib, required Map<String, String> params}) =>
    RustLib.instance.api.crateFeaturesFlutterDartFlatCrossPlatformAsdAsr(
        lib: lib, params: params);

void flatCrossPlatformConsumer(
        {required String lib,
        required Map<String, String> params,
        required RustConsumer consumer}) =>
    RustLib.instance.api.crateFeaturesFlutterDartFlatCrossPlatformConsumer(
        lib: lib, params: params, consumer: consumer);

Uint8List flatCrossPlatformConsumerData(
        {required String lib,
        required Map<String, String> params,
        required RustConsumer consumer}) =>
    RustLib.instance.api.crateFeaturesFlutterDartFlatCrossPlatformConsumerData(
        lib: lib, params: params, consumer: consumer);

void flatCrossPlatformFunction(
        {required String lib,
        required Map<String, String> params,
        required RustFunction function}) =>
    RustLib.instance.api.crateFeaturesFlutterDartFlatCrossPlatformFunction(
        lib: lib, params: params, function: function);

void flatCrossPlatformSupplier(
        {required String lib,
        required Map<String, String> params,
        required RustSupplier supplier}) =>
    RustLib.instance.api.crateFeaturesFlutterDartFlatCrossPlatformSupplier(
        lib: lib, params: params, supplier: supplier);

// Rust type: RustOpaqueMoi<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<RustConsumer>>
abstract class RustConsumer implements RustOpaqueInterface {
  factory RustConsumer(
          {required String uniqueId,
          required FutureOr<void> Function(Uint8List) consumer}) =>
      RustLib.instance.api.crateFeaturesFlutterDartRustConsumerNew(
          uniqueId: uniqueId, consumer: consumer);
}

// Rust type: RustOpaqueMoi<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<RustFunction>>
abstract class RustFunction implements RustOpaqueInterface {
  factory RustFunction(
          {required String uniqueId,
          required FutureOr<Uint8List> Function(Uint8List) function}) =>
      RustLib.instance.api.crateFeaturesFlutterDartRustFunctionNew(
          uniqueId: uniqueId, function: function);
}

// Rust type: RustOpaqueMoi<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<RustSupplier>>
abstract class RustSupplier implements RustOpaqueInterface {
  factory RustSupplier(
          {required String uniqueId,
          required FutureOr<Uint8List> Function() supplier}) =>
      RustLib.instance.api.crateFeaturesFlutterDartRustSupplierNew(
          uniqueId: uniqueId, supplier: supplier);
}
