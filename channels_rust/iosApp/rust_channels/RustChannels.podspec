Pod::Spec.new do |spec|
  spec.name         = "RustChannels"
  spec.version      = "0.1.0"
  spec.summary      = "Rust FFI Channel for iOS."
  spec.description  = <<-DESC
  Rust common FFI Channel for iOS.
                   DESC

  spec.homepage     = "https://git.haier.net/uplus/rust/channels_rust"
  spec.license      = "MIT"
  spec.author       = { "lubiao" => "<EMAIL>" }
  spec.platform     = :ios, "12.0"
  spec.swift_version = "5.0"
  spec.source       = { :git => "https://git.haier.net/uplus/shell/cocoapods/rust_channels.git", :tag => "#{spec.version}" }

  spec.source_files  = "**/*.{h,c,cpp,m,mm,swift}"
  spec.libraries = "resolv"

  spec.pod_target_xcconfig = {
    "DEFINES_MODULE" => "YES"
  }
  
  # spec.dependency 'RustUplus', '>= 0.1.0'

end
