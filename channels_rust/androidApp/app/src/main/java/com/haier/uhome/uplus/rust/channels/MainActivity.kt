package com.haier.uhome.uplus.rust.channels

import android.os.Bundle
import android.util.Log
import androidx.appcompat.app.AppCompatActivity
import com.haier.uhome.uplus.rust.channels.databinding.ActivityMainBinding
import com.haier.uhome.uplus.rust.security.RustSecurity

class MainActivity : AppCompatActivity() {
    companion object {
        private const val TAG = "MainActivity"
        private const val AES_KEY = "e4R5h5DDghLDJiBTaPk0lQludR9tqs0L5v+oo6zgD2Cdlr5SmgpG" +
                "F7Opl+kXoZK4oKnHEL0HwUUAR7HsY/2EVOhE9eSoSqsBATIwmZXqGRDp2KHTQIpx0rDThWrD" +
                "R7E9o6q0b5F3PoOO242mdGTw1Dr0tBDWH/nrkPIK52R6RZlS9dUgIiP9yu1E4caHj3osWVQq" +
                "rNC3CvWAW1IuSkH6KrckTyiqqunqC6Z/mwevUf/LGTRzq7ccdQfuMmwhSKugJhgWanOr6FHbP" +
                "D+HoswSXAUF1KYpdGBNtiMrZXXmB2awFpqjsLfnOyrrIU+FdaUlyYCLDmlOF9YCxyyvg0CZzQ=="
        private const val AES_IV = "6oESV0/uTwNicTnixpvpDQ=="

        init {
            System.loadLibrary("rust_uplus")
        }
    }

    private val binding by viewBinding<ActivityMainBinding>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding.init.setOnClickListener {
            val plainText = "你猜猜我是谁"
            RustSecurity.init()
            RustSecurity.rsaPubEncrypt("test rsa")?.let {
                Log.d(TAG, "onCreate: $it")
                val decode = RustSecurity.rsaPriDecrypt(it)
                Log.d(TAG, "onCreate: $decode")
            }
            RustSecurity.setAesKey(AES_KEY)
            RustSecurity.aesEncrypt(AES_IV, plainText)?.let {
                Log.d(TAG, "onCreate: $it")
                val decode = RustSecurity.aesDecrypt(AES_IV, it)
                Log.d(TAG, "onCreate: $decode")
            }
        }
    }
}