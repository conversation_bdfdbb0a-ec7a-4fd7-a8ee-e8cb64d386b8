// automatically generated by the <PERSON>Buffers compiler, do not modify

package com.haier.uhome.uplus.rust.security.fbs

import com.google.flatbuffers.BaseVector
import com.google.flatbuffers.BooleanVector
import com.google.flatbuffers.ByteVector
import com.google.flatbuffers.Constants
import com.google.flatbuffers.DoubleVector
import com.google.flatbuffers.FlatBufferBuilder
import com.google.flatbuffers.FloatVector
import com.google.flatbuffers.LongVector
import com.google.flatbuffers.StringVector
import com.google.flatbuffers.Struct
import com.google.flatbuffers.Table
import com.google.flatbuffers.UnionVector
import java.nio.ByteBuffer
import java.nio.ByteOrder
import kotlin.math.sign

@Suppress("unused")
class StrWrapper : Table() {

    fun __init(_i: Int, _bb: ByteBuffer)  {
        __reset(_i, _bb)
    }
    fun __assign(_i: Int, _bb: ByteBuffer) : StrWrapper {
        __init(_i, _bb)
        return this
    }
    val value : String?
        get() {
            val o = __offset(4)
            return if (o != 0) {
                __string(o + bb_pos)
            } else {
                null
            }
        }
    val valueAsByteBuffer : ByteBuffer get() = __vector_as_bytebuffer(4, 1)
    fun valueInByteBuffer(_bb: ByteBuffer) : ByteBuffer = __vector_in_bytebuffer(_bb, 4, 1)
    companion object {
        fun validateVersion() = Constants.FLATBUFFERS_24_3_25()
        fun getRootAsStrWrapper(_bb: ByteBuffer): StrWrapper = getRootAsStrWrapper(_bb, StrWrapper())
        fun getRootAsStrWrapper(_bb: ByteBuffer, obj: StrWrapper): StrWrapper {
            _bb.order(ByteOrder.LITTLE_ENDIAN)
            return (obj.__assign(_bb.getInt(_bb.position()) + _bb.position(), _bb))
        }
        fun createStrWrapper(builder: FlatBufferBuilder, valueOffset: Int) : Int {
            builder.startTable(1)
            addValue(builder, valueOffset)
            return endStrWrapper(builder)
        }
        fun startStrWrapper(builder: FlatBufferBuilder) = builder.startTable(1)
        fun addValue(builder: FlatBufferBuilder, value: Int) = builder.addOffset(0, value, 0)
        fun endStrWrapper(builder: FlatBufferBuilder) : Int {
            val o = builder.endTable()
            return o
        }
    }
}
