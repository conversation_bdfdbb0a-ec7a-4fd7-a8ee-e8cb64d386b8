package com.haier.uhome.uplus.rust.security

import android.util.Log
import com.haier.uhome.uplus.rust.channels.RustChannel
import com.haier.uhome.uplus.rust.security.fbs.SecurityFlat
import com.haier.uhome.uplus.rust.security.fbs.StrWrapper
import java.nio.ByteBuffer

/**
 * Created by liuqing.yang
 * 2024/10/31.
 */
object RustSecurity {
    private const val TAG = "RustSecurity"
    private const val LIB_NAME = "lib_security"
    private const val ACTION = "action"
    private const val ACTION_RSA_PUB_ENCRYPT = "rsa_pub_encrypt"
    private const val ACTION_RSA_PRI_DECRYPT = "rsa_pri_decrypt"
    private const val ACTION_SET_AES_KEY = "set_aes_key"
    private const val ACTION_AES_ENCRYPT = "aes_encrypt"
    private const val ACTION_AES_DECRYPT = "aes_decrypt"

    private const val PARAM_CONTENT = "content"
    private const val PARAM_IV = "iv"

    private const val CODE_OK = 0

    fun init() {
        runCatching {
            RustChannel.getRustBuffer(LIB_NAME, mapOf(ACTION to "init"))
        }.onFailure {
            Log.w(TAG, "init: ", it)
        }
    }

    fun rsaPubEncrypt(content: String): String? {
        return parseStringFromFlat(
            mapOf(
                ACTION to ACTION_RSA_PUB_ENCRYPT,
                PARAM_CONTENT to content,
            )
        )
    }

    fun rsaPriDecrypt(content: String): String? {
        return parseStringFromFlat(
            mapOf(
                ACTION to ACTION_RSA_PRI_DECRYPT,
                PARAM_CONTENT to content,
            )
        )
    }

    fun setAesKey(content: String) {
        val info = parseStringFromFlat(
            mapOf(
                ACTION to ACTION_SET_AES_KEY,
                PARAM_CONTENT to content,
            )
        )
        Log.d(TAG, "setAesKeyResult: $info")
    }

    fun aesEncrypt(iv: String, content: String): String? {
        return parseStringFromFlat(
            mapOf(
                ACTION to ACTION_AES_ENCRYPT,
                PARAM_IV to iv,
                PARAM_CONTENT to content,
            )
        )
    }

    fun aesDecrypt(iv: String, content: String): String? {
        return parseStringFromFlat(
            mapOf(
                ACTION to ACTION_AES_DECRYPT,
                PARAM_IV to iv,
                PARAM_CONTENT to content,
            )
        )
    }

    private fun parseStringFromFlat(params: Map<String, String>): String? {
        var buffer: ByteBuffer? = null
        try {
            buffer = RustChannel.getRustBuffer(LIB_NAME, params)
            if (buffer == null) {
                Log.w(TAG, "parseStringFromFlat: JNI return null")
                return null
            }
            val flat = SecurityFlat.getRootAsSecurityFlat(buffer)
            Log.d(TAG, "parseStringFromFlat: ${flat.code}, ${flat.error}")
            if (flat.code == CODE_OK) {
                val container = flat.container(StrWrapper()) as? StrWrapper
                container?.let {
                    return it.value
                }
                Log.d(TAG, "parseStringFromFlat: container is null")
            }
        } catch (e: Exception) {
            Log.w(TAG, "parseStringFromFlat: ", e)
        } finally {
            RustChannel.releaseBuffer(buffer)
        }
        return null
    }
}