package com.haier.uhome.uplus.rust.channels

import java.nio.ByteBuffer

/**
 * Created by liuqing.yang
 * 2024/9/26.
 */
object RustChannel {
    @Throws(Exception::class)
    fun manageRustListener(
        lib: String,
        params: Map<String, String>,
        rustChannelListener: RustChannelListener
    ) {
        RustFlat.flatCrossPlatformConsumer(lib, params, rustChannelListener)
    }

    @Throws(Exception::class)
    fun manageRustDataListener(
        lib: String,
        params: Map<String, String>,
        rustChannelListener: RustChannelListener
    ): ByteBuffer? {
        return RustFlat.flatCrossPlatformConsumerData(lib, params, rustChannelListener)
    }

    @Throws(Exception::class)
    fun getRustBuffer(lib: String, params: Map<String, String>): ByteBuffer? =
        RustFlat.flatCrossPlatform(lib, params)

    fun getRustBufferAsync(
        lib: String,
        params: Map<String, String>,
        rustChannelListener: RustChannelListener
    ) {
        RustFlat.flatCrossPlatformAsync(lib, params, rustChannelListener)
    }

    fun releaseBuffer(buffer: ByteBuffer?) {
        RustFlat.dropBuffer(buffer)
    }
}
