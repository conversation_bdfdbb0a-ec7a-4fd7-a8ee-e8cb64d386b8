// automatically generated by the FlatBuffers compiler, do not modify

package com.haier.uhome.uplus.rust.security.fbs

import com.google.flatbuffers.BaseVector
import com.google.flatbuffers.BooleanVector
import com.google.flatbuffers.ByteVector
import com.google.flatbuffers.Constants
import com.google.flatbuffers.DoubleVector
import com.google.flatbuffers.FlatBufferBuilder
import com.google.flatbuffers.FloatVector
import com.google.flatbuffers.LongVector
import com.google.flatbuffers.StringVector
import com.google.flatbuffers.Struct
import com.google.flatbuffers.Table
import com.google.flatbuffers.UnionVector
import java.nio.ByteBuffer
import java.nio.ByteOrder
import kotlin.math.sign

@Suppress("unused")
class NoneWrapper : Table() {

    fun __init(_i: Int, _bb: ByteBuffer)  {
        __reset(_i, _bb)
    }
    fun __assign(_i: Int, _bb: ByteBuffer) : NoneWrapper {
        __init(_i, _bb)
        return this
    }
    companion object {
        fun validateVersion() = Constants.FLATBUFFERS_24_3_25()
        fun getRootAsNoneWrapper(_bb: ByteBuffer): NoneWrapper = getRootAsNoneWrapper(_bb, NoneWrapper())
        fun getRootAsNoneWrapper(_bb: ByteBuffer, obj: NoneWrapper): NoneWrapper {
            _bb.order(ByteOrder.LITTLE_ENDIAN)
            return (obj.__assign(_bb.getInt(_bb.position()) + _bb.position(), _bb))
        }
        fun startNoneWrapper(builder: FlatBufferBuilder) = builder.startTable(0)
        fun endNoneWrapper(builder: FlatBufferBuilder) : Int {
            val o = builder.endTable()
            return o
        }
    }
}
