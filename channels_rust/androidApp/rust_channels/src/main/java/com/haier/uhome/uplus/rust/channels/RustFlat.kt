package com.haier.uhome.uplus.rust.channels

import java.nio.ByteBuffer

/**
 * Created by liuqing.yang
 * 2024/9/26.
 */
internal object RustFlat {
    external fun flatCrossPlatform(lib: String, params: Map<String, String>): ByteBuffer?

    external fun flatCrossPlatformAsync(
        lib: String,
        params: Map<String, String>,
        rustChannelListener: RustChannelListener
    ): ByteBuffer?

    external fun flatCrossPlatformConsumer(
        lib: String,
        params: Map<String, String>,
        rustChannelListener: RustChannelListener
    )

    external fun flatCrossPlatformConsumerData(
        lib: String,
        params: Map<String, String>,
        rustChannelListener: RustChannelListener
    ): ByteBuffer?

    external fun dropBuffer(byteBuffer: ByteBuffer?)
}