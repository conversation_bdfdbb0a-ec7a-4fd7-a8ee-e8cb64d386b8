pub const HELP: &str = r#"
A RSA encryption and decryption command line tool

Usage: rust_rsa [OPTIONS] [CONTENT]
Example: ./rust_rsa -aes-key [key] -aes-iv [iv content] -aes-e [data]

Options:
  -e, --encrypt <content>  Encrypt content using the public key
  -d, --decrypt <content>  Decrypt content using private key
  -aes-key <content>  指定 aes 秘钥，使用 rsa public key 进行加密
  -aes-iv  <content>  指定加密用的随机的 IV（初始化向量）
  -aes-e   <content>  使用 aes 加密
  -aes-d   <content>  使用 aes 解密
"#;

pub const PUBLIC_KEY: &str = r#"
-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAxEcXTiYFFcynn5X9/9Qr
P6dvKSKi4rmCd7jmBs/eTZe6LVnOmXHkRbtnD2TMLWTzDRYFhb6BZ9mkOeQncLwO
eddZV1R6SXUKn0aIIYHe4MlmSSCc5GHatSYCzfjoG0wOSNthyEeNoRu7J9QezxKm
UlEuNt55kCDNOwlnF6boE92bkowbCCdfY5uA3ZoBwvTumeKk5QIGRlJia5vcslnt
fH9CvdRzfV6G3d9p8oK8CLSGMOC9BokZ6qkI+fD8C0g9p31RVBtuMwRxkBgpN5gv
mhIlJq5YYZ9AcXJv5fe0MquIwwinDrmbGOdZWYPWvoiUvf/+3/K0dW1KwJlzQ+6f
ywIDAQAB
-----END PUBLIC KEY-----
"#;

pub const PRIVATE_KEY: &str = r#"
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"#;