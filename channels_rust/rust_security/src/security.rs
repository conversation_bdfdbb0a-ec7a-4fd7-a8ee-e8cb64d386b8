use crate::key::{PRIVATE_KEY, PUBLIC_KEY};
use base64::engine::general_purpose;
use base64::Engine;
use openssl::pkey::{Private, Public};
use openssl::rsa::{Padding, Rsa};
use openssl::symm::{Cip<PERSON>, Crypter, Mode};

pub struct Security {
    private_key: Rsa<Private>,
    public_key: Rsa<Public>,
    aes_key: Option<Vec<u8>>,
    cipher: Cipher,
}

impl Security {
    pub fn new() -> Security {
        Security {
            private_key: Rsa::private_key_from_pem(PRIVATE_KEY.to_string().as_bytes()).unwrap(),
            public_key: Rsa::public_key_from_pem(PUBLIC_KEY.to_string().as_bytes()).unwrap(),
            aes_key: None,
            cipher: Cipher::aes_256_cbc(),
        }
    }

    pub fn encrypt(&self, data: String) -> String {
        let mut encrypted = vec![0; self.public_key.size() as usize];
        let encrypted_len = self
            .public_key
            .public_encrypt(data.as_bytes(), &mut encrypted, Padding::PKCS1_OAEP)
            .unwrap();
        encrypted.truncate(encrypted_len);
        general_purpose::STANDARD.encode(&encrypted)
    }

    pub fn decrypt(&self, data: String) -> String {
        let mut decrypted = vec![0; self.private_key.size() as usize];
        let encrypted_bytes = general_purpose::STANDARD.decode(&data).unwrap();
        let decrypted_len = self
            .private_key
            .private_decrypt(&encrypted_bytes, &mut decrypted, Padding::PKCS1_OAEP)
            .unwrap();
        decrypted.truncate(decrypted_len);
        String::from_utf8(decrypted).unwrap()
    }

    pub fn set_aes_key(&mut self, key: String) {
        let mut decrypted = vec![0; self.private_key.size() as usize];
        let key_bytes = general_purpose::STANDARD.decode(&key).unwrap();
        let decrypted_len = self
            .private_key
            .private_decrypt(&key_bytes, &mut decrypted, Padding::PKCS1_OAEP)
            .unwrap();
        decrypted.truncate(decrypted_len);
        let key = general_purpose::STANDARD.decode(&decrypted).unwrap();
        self.aes_key = Some(key);
    }

    pub fn aes_encrypt(&self, iv: String, plaintext: String) -> String {
        let iv_bytes = general_purpose::STANDARD.decode(&iv).unwrap();
        let key = self.aes_key.clone().unwrap();
        let mut crypter = Crypter::new(self.cipher, Mode::Encrypt, &key, Some(&iv_bytes)).unwrap();
        let data = plaintext.as_bytes();
        let mut ciphertext = vec![0; data.len() + self.cipher.block_size()];
        let mut count = crypter.update(data, &mut ciphertext).unwrap();
        count += crypter.finalize(&mut ciphertext[count..]).unwrap();
        ciphertext.truncate(count);
        general_purpose::STANDARD.encode(&ciphertext)
    }

    pub fn aes_decrypt(&self, iv: String, text: String) -> String {
        let iv_bytes = general_purpose::STANDARD.decode(&iv).unwrap();
        let key = self.aes_key.clone().unwrap();
        let mut crypter = Crypter::new(self.cipher, Mode::Decrypt, &key, Some(&iv_bytes)).unwrap();
        let data = general_purpose::STANDARD.decode(&text).unwrap();
        let mut ciphertext = vec![0; data.len() + self.cipher.block_size()];
        let mut count = crypter.update(&data, &mut ciphertext).unwrap();
        count += crypter.finalize(&mut ciphertext[count..]).unwrap();
        ciphertext.truncate(count);
        String::from_utf8(ciphertext).unwrap()
    }
}
