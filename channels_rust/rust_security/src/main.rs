use crate::key::HELP;
use crate::security::Security;
use std::env;

mod key;
mod security;

fn main() {
    let args: Vec<String> = env::args().collect();
    let mut security = Security::new();
    match args.len() {
        3 => {
            let action = &args[1];
            let secret_msg = &args[2];
            if action == "-e" || action == "--encrypt" {
                let data = security.encrypt(secret_msg.to_string());
                println!("\n{}", data);
            } else if action == "-d" || action == "--decrypt" {
                let data = security.decrypt(secret_msg.to_string());
                println!("\n{}", data);
            } else {
                show_help()
            }
        }
        7 => {
            let key_action = &args[1];
            let key = &args[2];
            if key_action == "-aes-key" {
                security.set_aes_key(key.to_string());
            }
            let iv_action = &args[3];
            let iv = &args[4];
            let data_action = &args[5];
            let data = &args[6];
            if iv_action == "-aes-iv" && data_action == "-aes-e" {
                let plain = security.aes_encrypt(iv.to_string(), data.to_string());
                println!("\n{}", plain);
            } else if iv_action == "-aes-iv" && data_action == "-aes-d" {
                let plain = security.aes_decrypt(iv.to_string(), data.to_string());
                println!("\n{}", plain);
            } else {
                show_help();
            }
        }
        _ => show_help(),
    }
}

fn show_help() {
    println!("{}", HELP);
}
