use base64::engine::general_purpose;
use base64::Engine;
use openssl::rsa::{Padding, Rsa};
use std::fs::File;
use std::io::Read;

#[test]
fn encryption() {
    let mut private_key_file = File::open("private_key.pem").unwrap();
    let mut public_key_file = File::open("public_key.pem").unwrap();

    let mut private_key_pem = String::new();
    private_key_file
        .read_to_string(&mut private_key_pem)
        .unwrap();
    println!("private_key_pem is {}", private_key_pem);

    let mut public_key_pem = String::new();
    public_key_file.read_to_string(&mut public_key_pem).unwrap();
    println!("public_key_pem is {}", public_key_pem);

    let private_key = Rsa::private_key_from_pem(private_key_pem.as_bytes()).unwrap();
    let public_key = Rsa::public_key_from_pem(public_key_pem.as_bytes()).unwrap();

    let data = "This is a secret message";
    println!("Original: {}", data);

    let mut encrypted = vec![0; public_key.size() as usize];
    let encrypted_len = public_key
        .public_encrypt(data.as_bytes(), &mut encrypted, Padding::PKCS1_OAEP)
        .unwrap();
    encrypted.truncate(encrypted_len);
    let encrypted_base64 = general_purpose::STANDARD.encode(&encrypted);
    println!("Encrypted (Base64): {}", encrypted_base64);
    //
    let mut decrypted = vec![0; private_key.size() as usize];
    let encrypted_bytes = general_purpose::STANDARD.decode(&encrypted_base64).unwrap();
    let decrypted_len = private_key
        .private_decrypt(&encrypted_bytes, &mut decrypted, Padding::PKCS1_OAEP)
        .unwrap();
    decrypted.truncate(decrypted_len);
    let decrypted_data =
        String::from_utf8(decrypted).expect("Failed to convert decrypted data to string");
    println!("Decrypted: {}", decrypted_data);
}
