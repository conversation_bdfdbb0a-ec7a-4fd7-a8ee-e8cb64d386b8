use std::env;

fn main() {
    let target = env::var("TARGET").unwrap();
    match target.as_str() {
        "aarch64-linux-android" => {
            println!("cargo:rustc-link-arg=-Wl,-z,max-page-size=16384");
            println!("cargo:rustc-link-search=native=libs/arm64-v8a");
            println!("cargo:rustc-link-lib=sqlite3");
        }
        "armv7-linux-androideabi" => {
            println!("cargo:rustc-link-search=native=libs/armeabi-v7a");
            println!("cargo:rustc-link-lib=sqlite3");
        }
        "i686-linux-android" => {}
        "x86_64-linux-android" => {}
        "aarch64-unknown-linux-ohos" => {
            println!("cargo:rustc-link-arg=-Wl,-z,max-page-size=16384");
            println!("cargo:rustc-link-search=native=libs/ohos-arm64-v8a");
            println!("cargo:rustc-link-lib=sqlite3");
            napi_build_ohos::setup();
        }
        "armv7-unknown-linux-ohos" => {}
        "x86_64-unknown-linux-ohos" => {}
        "aarch64-apple-ios" => {}
        "aarch64-apple-ios-sim" => {}
        "x86_64-apple-ios" => {}
        _ => {}
    }
}