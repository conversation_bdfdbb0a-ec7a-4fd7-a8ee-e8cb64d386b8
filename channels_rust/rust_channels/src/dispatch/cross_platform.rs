use crate::logic::lib_security::lib_security_cross_platform;
use log::debug;
use std::collections::HashMap;
use task_manager::platform::function::{PlatformConsumer, PlatformFunction, PlatformSupplier};

pub fn cross_platform(lib: &str, params: HashMap<String, String>) -> Vec<u8> {
    debug!("cross_platform: lib={}, params={:?}", lib, params);
    match lib {
        "lib_security" => lib_security_cross_platform(params),
        _ => {
            debug!("Unrecognized lib: {}", lib);
            Vec::new()
        }
    }
}

pub fn cross_platform_consumer(
    lib: &str,
    params: HashMap<String, String>,
    pc: impl PlatformConsumer + 'static,
) {
    debug!("cross_platform_consumer: lib={}, params={:?}", lib, params);
    match lib {
        _ => {
            debug!("Unrecognized lib: {}", lib);
        }
    }
}

pub fn cross_platform_consumer_data(
    lib: &str,
    params: HashMap<String, String>,
    pc: impl PlatformConsumer + 'static,
) -> Vec<u8> {
    debug!(
        "cross_platform_consumer_data: lib={}, params={:?}",
        lib, params
    );
    match lib {
        _ => {
            debug!("Unrecognized lib: {}", lib);
            Vec::new()
        }
    }
}

pub fn cross_platform_function(
    lib: &str,
    params: HashMap<String, String>,
    pf: impl PlatformFunction + 'static,
) {
    debug!("cross_platform_function: lib={}, params={:?}", lib, params);
}

pub fn cross_platform_supplier(
    lib: &str,
    params: HashMap<String, String>,
    ps: impl PlatformSupplier + 'static,
) {
    debug!("cross_platform_supplier: lib={}, params={:?}", lib, params);
}

pub async fn cross_platform_async(lib: &str, params: HashMap<String, String>) -> Vec<u8> {
    debug!("cross_platform_async: lib={}, params={:?}", lib, params);
    match lib {
        _ => {
            debug!("Unrecognized lib: {}", lib);
            Vec::new()
        }
    }
}
