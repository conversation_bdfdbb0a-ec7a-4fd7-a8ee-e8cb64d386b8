use crate::dispatch::cross_platform::{
    cross_platform, cross_platform_async, cross_platform_consumer, cross_platform_consumer_data,
    cross_platform_function, cross_platform_supplier,
};
use jni::errors::Result;
use jni::objects::{GlobalRef, JByteBuffer, JClass, JMap, JObject, JString, JValue};
use jni::sys::jobject;
use jni::JNIEnv;
use log::{warn, LevelFilter};
use parking_lot::RwLock;
use std::collections::HashMap;
use std::mem::ManuallyDrop;
use task_manager::common_runtime::get_runtime;
use task_manager::features::android::{get_jni_env, jobj_hash_code};
use task_manager::platform::function::{PlatformConsumer, PlatformFunction, PlatformSupplier};

struct JniListener {
    glis: Option<GlobalRef>,
    unique_id: String,
    gr_apply: RwLock<Option<GlobalRef>>,
    gr_get: RwLock<Option<GlobalRef>>,
}

impl JniListener {
    fn new(glis: GlobalRef) -> Self {
        let unique_id = jobj_hash_code(glis.as_obj());
        JniListener {
            glis: Some(glis),
            unique_id: unique_id.to_string(),
            gr_apply: RwLock::new(None),
            gr_get: RwLock::new(None),
        }
    }

    fn handle_accept(&self, data: Vec<u8>) -> Result<()> {
        let mut env = get_jni_env()?;
        let length = data.len();
        let mut vec = ManuallyDrop::new(data);
        let ptr = vec.as_mut_ptr();
        let jbuf = unsafe { env.new_direct_byte_buffer(ptr, length)? };
        match &self.glis {
            None => {
                warn!("handle_accept: glis is none");
            }
            Some(g) => {
                env.call_method(
                    g.as_obj(),
                    "accept",
                    "(Ljava/nio/ByteBuffer;)V",
                    &[JValue::from(&JObject::from(jbuf))],
                )?;
            }
        }
        Ok(())
    }

    fn handle_apply(&self, data: Vec<u8>) -> Result<Vec<u8>> {
        let mut env = get_jni_env()?;
        let length = data.len();
        let mut vec = ManuallyDrop::new(data);
        let ptr = vec.as_mut_ptr();
        let jbuf = unsafe { env.new_direct_byte_buffer(ptr, length)? };
        match &self.glis {
            None => {
                warn!("handle_apply: glis is none");
                Ok(vec![])
            }
            Some(g) => {
                let jvo = env.call_method(
                    g.as_obj(),
                    "apply",
                    "(Ljava/nio/ByteBuffer;)Ljava/nio/ByteBuffer;",
                    &[JValue::from(&JObject::from(jbuf))],
                )?;
                let jbf = JByteBuffer::from(jvo.l()?);
                let jbf_ptr = env.get_direct_buffer_address(&jbf)?;
                let jbf_ptr_len = env.get_direct_buffer_capacity(&jbf)?;
                let jbf_gr = env.new_global_ref(jbf)?;
                let mut gr_write = self.gr_apply.write();
                *gr_write = Some(jbf_gr);
                unsafe { Ok(Vec::from_raw_parts(jbf_ptr, jbf_ptr_len, jbf_ptr_len)) }
            }
        }
    }

    fn handle_get(&self) -> Result<Vec<u8>> {
        let mut env = get_jni_env()?;
        match &self.glis {
            None => {
                warn!("handle_get: glis is none");
                Ok(vec![])
            }
            Some(g) => {
                let jvo = env.call_method(g.as_obj(), "apply", "()Ljava/nio/ByteBuffer;", &[])?;
                let jbf = JByteBuffer::from(jvo.l()?);
                let jbf_ptr = env.get_direct_buffer_address(&jbf)?;
                let jbf_ptr_len = env.get_direct_buffer_capacity(&jbf)?;
                let jbf_gr = env.new_global_ref(jbf)?;
                let mut gr_write = self.gr_get.write();
                *gr_write = Some(jbf_gr);
                unsafe { Ok(Vec::from_raw_parts(jbf_ptr, jbf_ptr_len, jbf_ptr_len)) }
            }
        }
    }
}

impl PlatformConsumer for JniListener {
    fn accept(&self, data: Vec<u8>) {
        if let Err(e) = self.handle_accept(data) {
            warn!("JniListener: accept err: {}", e.to_string());
        }
    }

    fn unique_id(&self) -> &str {
        &self.unique_id
    }
}

impl PlatformFunction for JniListener {
    fn apply(&self, data: Vec<u8>) -> Vec<u8> {
        self.handle_apply(data).unwrap_or_else(|e| {
            warn!("JniListener: apply err: {}", e.to_string());
            vec![]
        })
    }

    fn unique_id(&self) -> &str {
        &self.unique_id
    }
}

impl PlatformSupplier for JniListener {
    fn get(&self) -> Vec<u8> {
        self.handle_get().unwrap_or_else(|e| {
            warn!("JniListener: get err: {}", e.to_string());
            vec![]
        })
    }

    fn unique_id(&self) -> &str {
        &self.unique_id
    }
}

#[no_mangle]
extern "system" fn Java_com_haier_uhome_uplus_rust_channels_RustFlat_flatCrossPlatform(
    mut env: JNIEnv,
    _: JClass,
    jlib: JString,
    jmap: JObject,
) -> jobject {
    flat_cross_platform(&mut env, &jlib, &jmap).unwrap_or_else(|e| {
        throw_illegal_state(&mut env, e.to_string().as_str());
        null()
    })
}

#[no_mangle]
extern "system" fn Java_com_haier_uhome_uplus_rust_channels_RustFlat_flatCrossPlatformConsumer(
    mut env: JNIEnv,
    _: JClass,
    jlib: JString,
    jmap: JObject,
    jlistener: JObject,
) {
    flat_cross_platform_consumer(&mut env, &jlib, &jmap, jlistener).unwrap_or_else(|e| {
        throw_illegal_state(&mut env, e.to_string().as_str());
    })
}

#[no_mangle]
extern "system" fn Java_com_haier_uhome_uplus_rust_channels_RustFlat_flatCrossPlatformConsumerData(
    mut env: JNIEnv,
    _: JClass,
    jlib: JString,
    jmap: JObject,
    jlistener: JObject,
) -> jobject {
    flat_cross_platform_consumer_data(&mut env, &jlib, &jmap, jlistener).unwrap_or_else(|e| {
        throw_illegal_state(&mut env, e.to_string().as_str());
        null()
    })
}

#[no_mangle]
extern "system" fn Java_com_haier_uhome_uplus_rust_channels_RustFlat_flatCrossPlatformFunction(
    mut env: JNIEnv,
    _: JClass,
    jlib: JString,
    jmap: JObject,
    jlistener: JObject,
) {
    flat_cross_platform_function(&mut env, &jlib, &jmap, jlistener).unwrap_or_else(|e| {
        throw_illegal_state(&mut env, e.to_string().as_str());
    })
}

#[no_mangle]
extern "system" fn Java_com_haier_uhome_uplus_rust_channels_RustFlat_flatCrossPlatformSupplier(
    mut env: JNIEnv,
    _: JClass,
    jlib: JString,
    jmap: JObject,
    jlistener: JObject,
) {
    flat_cross_platform_supplier(&mut env, &jlib, &jmap, jlistener).unwrap_or_else(|e| {
        throw_illegal_state(&mut env, e.to_string().as_str());
    })
}

#[no_mangle]
extern "system" fn Java_com_haier_uhome_uplus_rust_channels_RustFlat_flatCrossPlatformAsync(
    mut env: JNIEnv,
    _: JClass,
    jlib: JString,
    jmap: JObject,
    jlistener: JObject,
) {
    flat_cross_platform_async(&mut env, &jlib, &jmap, jlistener).unwrap_or_else(|e| {
        throw_illegal_state(&mut env, e.to_string().as_str());
    })
}

#[no_mangle]
pub extern "system" fn Java_com_haier_uhome_uplus_rust_channels_RustFlat_dropBuffer(
    mut env: JNIEnv,
    _: JClass,
    jbuf: jobject,
) {
    drop_buffer(&env, jbuf).unwrap_or_else(|e| {
        throw_illegal_state(&mut env, e.to_string().as_str());
    })
}

fn flat_cross_platform(env: &mut JNIEnv, jlib: &JString, jmap: &JObject) -> Result<jobject> {
    let lib = jstring_to_string(env, &jlib)?;
    let params = jmap_to_map(env, &jmap)?;
    let vec: Vec<u8> = cross_platform(lib.as_str(), params);
    map_buffer(env, vec)
}

fn flat_cross_platform_consumer(
    env: &mut JNIEnv,
    jlib: &JString,
    jmap: &JObject,
    jlistener: JObject,
) -> Result<()> {
    let lib = jstring_to_string(env, &jlib)?;
    let params = jmap_to_map(env, &jmap)?;
    let jni_listener = JniListener::new(env.new_global_ref(jlistener)?);
    Ok(cross_platform_consumer(lib.as_str(), params, jni_listener))
}

fn flat_cross_platform_consumer_data(
    env: &mut JNIEnv,
    jlib: &JString,
    jmap: &JObject,
    jlistener: JObject,
) -> Result<jobject> {
    let lib = jstring_to_string(env, &jlib)?;
    let params = jmap_to_map(env, &jmap)?;
    let jni_listener = JniListener::new(env.new_global_ref(jlistener)?);
    map_buffer(
        env,
        cross_platform_consumer_data(lib.as_str(), params, jni_listener),
    )
}

fn flat_cross_platform_function(
    env: &mut JNIEnv,
    jlib: &JString,
    jmap: &JObject,
    jlistener: JObject,
) -> Result<()> {
    let lib = jstring_to_string(env, &jlib)?;
    let params = jmap_to_map(env, &jmap)?;
    let jni_listener = JniListener::new(env.new_global_ref(jlistener)?);
    Ok(cross_platform_function(lib.as_str(), params, jni_listener))
}

fn flat_cross_platform_supplier(
    env: &mut JNIEnv,
    jlib: &JString,
    jmap: &JObject,
    jlistener: JObject,
) -> Result<()> {
    let lib = jstring_to_string(env, &jlib)?;
    let params = jmap_to_map(env, &jmap)?;
    let jni_listener = JniListener::new(env.new_global_ref(jlistener)?);
    Ok(cross_platform_supplier(lib.as_str(), params, jni_listener))
}

fn flat_cross_platform_async(
    env: &mut JNIEnv,
    jlib: &JString,
    jmap: &JObject,
    jlistener: JObject,
) -> Result<()> {
    let lib = jstring_to_string(env, &jlib)?;
    let params = jmap_to_map(env, &jmap)?;
    let jni_listener = JniListener::new(env.new_global_ref(jlistener)?);
    get_runtime().spawn(async move {
        let data = cross_platform_async(lib.as_str(), params).await;
        jni_listener.accept(data);
    });
    Ok(())
}

fn drop_buffer(env: &JNIEnv, jbuf: jobject) -> Result<()> {
    if jbuf.is_null() {
        return Ok(());
    }
    unsafe {
        let buffer = JByteBuffer::from_raw(jbuf);
        let ptr = env.get_direct_buffer_address(&buffer)?;
        let len = env.get_direct_buffer_capacity(&buffer)?;
        Vec::from_raw_parts(ptr, len, len);
    }
    Ok(())
}

fn map_buffer(env: &mut JNIEnv, data: Vec<u8>) -> Result<jobject> {
    if data.is_empty() {
        Ok(null())
    } else {
        let length = data.len();
        let mut vec = ManuallyDrop::new(data);
        let ptr = vec.as_mut_ptr();
        unsafe { Ok(env.new_direct_byte_buffer(ptr, length)?.into_raw()) }
    }
}

fn jmap_to_map(env: &mut JNIEnv, jmap: &JObject) -> Result<HashMap<String, String>> {
    let jparams = JMap::from_env(env, jmap)?;
    let mut params = HashMap::new();
    let mut iter = jparams.iter(env)?;
    loop {
        let it = iter.next(env)?;
        if it.is_none() {
            break;
        }
        let (k, v) = it.unwrap();
        let key: String = env.get_string(&JString::from(k))?.into();
        let value: String = env.get_string(&JString::from(v))?.into();
        params.insert(key, value);
    }
    Ok(params)
}

fn jstring_to_string(env: &mut JNIEnv, jstring: &JString) -> Result<String> {
    let java_str = env.get_string(jstring)?;
    Ok(java_str.into())
}

fn throw_illegal_state(env: &mut JNIEnv, msg: &str) {
    env.throw_new("java/lang/IllegalStateException", msg)
        .unwrap_or_default()
}

fn null() -> jobject {
    JObject::null().into_raw()
}

pub fn init_logger() {
    android_logger::init_once(
        android_logger::Config::default()
            .with_max_level(LevelFilter::Debug)
            .with_tag("LibRustUplus")
            .format(|f, record| {
                let module_path = record.module_path().unwrap_or("unknown::module").to_owned();
                let file = record.file().unwrap_or("unknown.rs").to_owned();
                let line = record.line().unwrap_or(0).to_owned();
                write!(f, "{}/{}:{} {}", module_path, file, line, record.args())
            }),
    );
}
