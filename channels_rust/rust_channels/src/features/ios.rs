use crate::dispatch::cross_platform::{
    cross_platform, cross_platform_async, cross_platform_consumer, cross_platform_consumer_data,
    cross_platform_function, cross_platform_supplier,
};
use log::{debug, error};
use std::collections::HashMap;
use std::ffi::{c_int, c_uchar, c_void, CStr};
use std::os::raw::c_char;
use std::sync::Arc;
use task_manager::common_runtime::get_runtime;
use task_manager::platform::function::{PlatformConsumer, PlatformFunction, PlatformSupplier};
use thiserror::Error;
#[derive(Error, Debug)]
pub enum IosError {
    #[error("Invalid UTF-8 string")]
    InvalidUtf8,
    #[error("Invalid parameter format")]
    InvalidParamFormat,
    #[error("Null pointer provided")]
    NullPointer,
    #[error("Invalid JSON: {0}")]
    J<PERSON>(serde_json::Error),
}
type Result<T> = std::result::Result<T, IosError>;

const ERROR_CODE: c_int = -1;

const SUCCESS_CODE: c_int = 0;

#[repr(C)]
pub struct IosBufferData {
    buffer: *const c_uchar,
    length: c_int,
}

#[repr(C)]
pub struct IosReturnBuffer {
    buffer: *mut c_uchar,
    length: c_int,
    capacity: c_int,
}

struct IosListener {
    callback: extern "C" fn(buffer_data: IosBufferData, ios_callback: *const c_void),
    unique_id: String,
    ios_callback: Arc<usize>,
    apply_callback: Option<extern "C" fn(buffer_data: IosBufferData) -> IosReturnBuffer>,
    get_callback: Option<extern "C" fn() -> IosReturnBuffer>,
}

impl IosListener {
    fn new(
        callback: extern "C" fn(buffer_data: IosBufferData, ios_callback: *const c_void),
        unique_id: String,
        ios_callback: *const c_void,
        apply_callback: Option<extern "C" fn(buffer_data: IosBufferData) -> IosReturnBuffer>,
        get_callback: Option<extern "C" fn() -> IosReturnBuffer>,
    ) -> Self {
        let ios_callback = unsafe { Arc::from_raw(ios_callback as *const usize) };
        IosListener {
            callback,
            unique_id,
            ios_callback,
            apply_callback,
            get_callback,
        }
    }

    fn handle_accept(&self, data: Vec<u8>) {
        (self.callback)(
            IosBufferData {
                length: data.len() as c_int,
                buffer: Box::into_raw(data.into_boxed_slice()) as *mut c_uchar,
            },
            Arc::as_ptr(&self.ios_callback) as *const c_void,
        );
    }

    fn handle_apply(&self, data: Vec<u8>) -> Vec<u8> {
        if let Some(apply_callback) = self.apply_callback {
            let input = IosBufferData {
                length: data.len() as c_int,
                buffer: Box::into_raw(data.into_boxed_slice()) as *mut c_uchar,
            };
            let result = apply_callback(input);
            unsafe {
                Vec::from_raw_parts(
                    result.buffer,
                    result.length as usize,
                    result.capacity as usize,
                )
            }
        } else {
            debug!("Apply callback not set");
            Vec::new()
        }
    }

    fn handle_get(&self) -> Vec<u8> {
        if let Some(get_callback) = self.get_callback {
            let result = get_callback();
            unsafe {
                Vec::from_raw_parts(
                    result.buffer,
                    result.length as usize,
                    result.capacity as usize,
                )
            }
        } else {
            debug!("Get callback not set");
            Vec::new()
        }
    }
}

impl PlatformConsumer for IosListener {
    fn accept(&self, data: Vec<u8>) {
        self.handle_accept(data);
    }
    fn unique_id(&self) -> &str {
        &self.unique_id
    }
}

impl PlatformFunction for IosListener {
    fn apply(&self, data: Vec<u8>) -> Vec<u8> {
        self.handle_apply(data)
    }

    fn unique_id(&self) -> &str {
        &self.unique_id
    }
}

impl PlatformSupplier for IosListener {
    fn get(&self) -> Vec<u8> {
        self.handle_get()
    }

    fn unique_id(&self) -> &str {
        &self.unique_id
    }
}

fn c_str_to_hashmap(c_str: *const c_char) -> Result<HashMap<String, String>> {
    let rust_str = unsafe {
        CStr::from_ptr(c_str)
            .to_str()
            .map_err(|_| IosError::InvalidUtf8)?
    };
    let map: HashMap<String, String> = serde_json::from_str(rust_str).map_err(IosError::Json)?;
    Ok(map)
}

fn parse_params(
    lib: *const c_char,
    params: *const c_char,
) -> Result<(String, HashMap<String, String>)> {
    let lib = unsafe {
        CStr::from_ptr(lib)
            .to_str()
            .map_err(|_| IosError::InvalidUtf8)?
    };
    let params = c_str_to_hashmap(params)?;
    Ok((lib.to_string(), params))
}
fn process_unique_id(unique_id: *const c_char) -> Result<String> {
    unsafe {
        if !unique_id.is_null() {
            CStr::from_ptr(unique_id)
                .to_str()
                .map(String::from)
                .map_err(|_| IosError::InvalidUtf8)
        } else {
            Err(IosError::NullPointer)
        }
    }
}

#[no_mangle]
pub extern "C" fn ios_flat_cross_platform(
    lib: *const c_char,
    params: *const c_char,
) -> IosReturnBuffer {
    match parse_params(lib, params) {
        Ok((lib, params)) => {
            let vec: Vec<u8> = cross_platform(&lib, params);
            IosReturnBuffer {
                length: vec.len() as c_int,
                capacity: vec.capacity() as c_int,
                buffer: Box::into_raw(vec.into_boxed_slice()) as *mut c_uchar,
            }
        }
        Err(e) => {
            error!("Failed to parse parameters: {:?}", e);
            IosReturnBuffer {
                buffer: std::ptr::null_mut(),
                length: 0,
                capacity: 0,
            }
        }
    }
}

#[no_mangle]
pub extern "C" fn ios_flat_cross_platform_consumer(
    lib: *const c_char,
    params: *const c_char,
    callback: extern "C" fn(buffer_data: IosBufferData, ios_callback: *const c_void),
    unique_id: *const c_char,
    ios_callback: *const c_void,
) -> c_int {
    match parse_params(lib, params) {
        Ok((lib, params)) => {
            let unique_id = match process_unique_id(unique_id) {
                Ok(s) => s,
                Err(e) => {
                    error!("Failed to process unique ID: {:?}", e);
                    return ERROR_CODE;
                }
            };
            let ios_listener = IosListener::new(callback, unique_id, ios_callback, None, None);
            cross_platform_consumer(&lib, params, ios_listener);
            SUCCESS_CODE
        }
        Err(e) => {
            error!("Failed to parse parameters: {:?}", e);
            ERROR_CODE
        }
    }
}

#[no_mangle]
pub extern "C" fn ios_flat_cross_platform_consumer_data(
    lib: *const c_char,
    params: *const c_char,
    callback: extern "C" fn(buffer_data: IosBufferData, ios_callback: *const c_void),
    unique_id: *const c_char,
    ios_callback: *const c_void,
) -> IosReturnBuffer {
    match parse_params(lib, params) {
        Ok((lib, params)) => {
            let unique_id = match process_unique_id(unique_id) {
                Ok(s) => s,
                Err(e) => {
                    error!("Failed to process unique ID: {:?}", e);
                    return IosReturnBuffer {
                        buffer: std::ptr::null_mut(),
                        length: 0,
                        capacity: 0,
                    };
                }
            };
            let ios_listener = IosListener::new(callback, unique_id, ios_callback, None, None);
            let vec = cross_platform_consumer_data(&lib, params, ios_listener);
            IosReturnBuffer {
                length: vec.len() as c_int,
                capacity: vec.capacity() as c_int,
                buffer: Box::into_raw(vec.into_boxed_slice()) as *mut c_uchar,
            }
        }
        Err(e) => {
            error!("Failed to parse parameters: {:?}", e);
            IosReturnBuffer {
                buffer: std::ptr::null_mut(),
                length: 0,
                capacity: 0,
            }
        }
    }
}

#[no_mangle]
pub extern "C" fn ios_flat_cross_platform_function(
    lib: *const c_char,
    params: *const c_char,
    callback: extern "C" fn(buffer_data: IosBufferData, ios_callback: *const c_void),
    apply_callback: extern "C" fn(buffer_data: IosBufferData) -> IosReturnBuffer,
    unique_id: *const c_char,
    ios_callback: *const c_void,
) -> c_int {
    match parse_params(lib, params) {
        Ok((lib, params)) => {
            let unique_id = match process_unique_id(unique_id) {
                Ok(s) => s,
                Err(e) => {
                    error!("Failed to process unique ID: {:?}", e);
                    return ERROR_CODE;
                }
            };
            let ios_listener = IosListener::new(
                callback,
                unique_id,
                ios_callback,
                Some(apply_callback),
                None,
            );
            cross_platform_function(&lib, params, ios_listener);
            SUCCESS_CODE
        }
        Err(e) => {
            error!("Failed to parse parameters: {:?}", e);
            ERROR_CODE
        }
    }
}

#[no_mangle]
pub extern "C" fn ios_flat_cross_platform_supplier(
    lib: *const c_char,
    params: *const c_char,
    callback: extern "C" fn(buffer_data: IosBufferData, ios_callback: *const c_void),
    get_callback: extern "C" fn() -> IosReturnBuffer,
    unique_id: *const c_char,
    ios_callback: *const c_void,
) -> c_int {
    match parse_params(lib, params) {
        Ok((lib, params)) => {
            let unique_id = match process_unique_id(unique_id) {
                Ok(s) => s,
                Err(e) => {
                    error!("Failed to process unique ID: {:?}", e);
                    return ERROR_CODE;
                }
            };
            let ios_listener =
                IosListener::new(callback, unique_id, ios_callback, None, Some(get_callback));
            cross_platform_supplier(&lib, params, ios_listener);
            SUCCESS_CODE
        }
        Err(e) => {
            error!("Failed to parse parameters: {:?}", e);
            ERROR_CODE
        }
    }
}

#[no_mangle]
pub extern "C" fn ios_flat_cross_platform_async(
    lib: *const c_char,
    params: *const c_char,
    callback: extern "C" fn(buffer_data: IosBufferData, ios_callback: *const c_void),
    ios_callback: *const c_void,
) -> c_int {
    match parse_params(lib, params) {
        Ok((lib, params)) => {
            let runtime = get_runtime();
            let callback_ptr = ios_callback as usize;
            runtime.spawn(async move {
                let result = cross_platform_async(&lib, params).await;
                callback(
                    IosBufferData {
                        length: result.len() as c_int,
                        buffer: Box::into_raw(result.into_boxed_slice()) as *mut c_uchar,
                    },
                    callback_ptr as *const c_void,
                );
            });
            SUCCESS_CODE
        }
        Err(e) => {
            error!("Failed to parse parameters: {:?}", e);
            ERROR_CODE
        }
    }
}

#[no_mangle]
pub extern "C" fn ios_drop_buffer(buffer: IosReturnBuffer) {
    if !buffer.buffer.is_null() && buffer.length > 0 {
        unsafe {
            let _ = Vec::from_raw_parts(
                buffer.buffer,
                buffer.length as usize,
                buffer.capacity as usize,
            );
        }
    }
}

#[no_mangle]
pub extern "C" fn ios_drop_buffer_data(buffer: IosBufferData) {
    if !buffer.buffer.is_null() && buffer.length > 0 {
        unsafe {
            let _ = Vec::from_raw_parts(
                buffer.buffer as *mut c_uchar,
                buffer.length as usize,
                buffer.length as usize,
            );
        }
    }
}
