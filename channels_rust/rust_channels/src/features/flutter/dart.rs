use crate::dispatch::cross_platform::{
    cross_platform, cross_platform_async, cross_platform_consumer, cross_platform_consumer_data,
    cross_platform_function, cross_platform_supplier,
};
use flutter_rust_bridge::{frb, DartFnFuture};
use std::collections::HashMap;
use std::sync::Arc;
use task_manager::common_runtime::get_runtime;
use task_manager::platform::function::{PlatformConsumer, PlatformFunction, PlatformSupplier};

/// FRB 对于同步和异步支持的说明
/// https://cjycode.com/flutter_rust_bridge/guides/concurrency/overview
/// Sync Dart + Async Rust 是不合理的
/// * 推荐使用：
/// * Async Dart + Sync Rust
/// * Async Dart + Async Rust
/// * Sync Dart + Sync Rust
///
/// `ZeroCopyBuffer<T>` is no longer needed, since zero-copy is automatically utilized,
/// just directly use `T` instead.

/// Sync Dart + Sync Rust
#[frb(sync)]
pub fn flat_cross_platform_sd_sr(lib: &str, params: HashMap<String, String>) -> Vec<u8> {
    cross_platform(lib, params)
}

/// Async Dart + Sync Rust
pub fn flat_cross_platform_asd_sr(lib: &str, params: HashMap<String, String>) -> Vec<u8> {
    cross_platform(lib, params)
}

/// Async Dart + Async Rust
pub async fn flat_cross_platform_asd_asr(lib: &str, params: HashMap<String, String>) -> Vec<u8> {
    cross_platform_async(lib, params).await
}

// Dart 添加监听器 ---

#[frb(sync)]
pub fn flat_cross_platform_consumer(
    lib: &str,
    params: HashMap<String, String>,
    consumer: RustConsumer,
) {
    cross_platform_consumer(lib, params, consumer)
}

#[frb(sync)]
pub fn flat_cross_platform_consumer_data(
    lib: &str,
    params: HashMap<String, String>,
    consumer: RustConsumer,
) -> Vec<u8> {
    cross_platform_consumer_data(lib, params, consumer)
}

#[frb(sync)]
pub fn flat_cross_platform_function(
    lib: &str,
    params: HashMap<String, String>,
    function: RustFunction,
) {
    cross_platform_function(lib, params, function)
}

#[frb(sync)]
pub fn flat_cross_platform_supplier(
    lib: &str,
    params: HashMap<String, String>,
    supplier: RustSupplier,
) {
    cross_platform_supplier(lib, params, supplier)
}

pub struct RustConsumer {
    unique_id: String,
    consumer: Arc<dyn Fn(Vec<u8>) -> DartFnFuture<()> + Send + Sync>,
}

impl RustConsumer {
    #[frb(sync)]
    pub fn new(
        unique_id: String,
        consumer: impl Fn(Vec<u8>) -> DartFnFuture<()> + Send + Sync + 'static,
    ) -> Self {
        Self {
            unique_id,
            consumer: Arc::new(consumer),
        }
    }
}

impl PlatformConsumer for RustConsumer {
    fn accept(&self, data: Vec<u8>) {
        let consumer = self.consumer.clone();
        get_runtime().spawn(async move {
            consumer(data).await;
        });
    }

    fn unique_id(&self) -> &str {
        &self.unique_id
    }
}

pub struct RustFunction {
    unique_id: String,
    function: Box<dyn Fn(Vec<u8>) -> DartFnFuture<Vec<u8>> + Send + Sync>,
}

impl RustFunction {
    #[frb(sync)]
    pub fn new(
        unique_id: String,
        function: impl Fn(Vec<u8>) -> DartFnFuture<Vec<u8>> + Send + Sync + 'static,
    ) -> Self {
        Self {
            unique_id,
            function: Box::new(function),
        }
    }
}

impl PlatformFunction for RustFunction {
    fn apply(&self, data: Vec<u8>) -> Vec<u8> {
        get_runtime().block_on(async {
            let f = &self.function;
            f(data).await
        })
    }

    fn unique_id(&self) -> &str {
        &self.unique_id
    }
}

pub struct RustSupplier {
    unique_id: String,
    supplier: Box<dyn Fn() -> DartFnFuture<Vec<u8>> + Send + Sync>,
}

impl RustSupplier {
    #[frb(sync)]
    pub fn new(
        unique_id: String,
        supplier: impl Fn() -> DartFnFuture<Vec<u8>> + Send + Sync + 'static,
    ) -> Self {
        Self {
            unique_id,
            supplier: Box::new(supplier),
        }
    }
}

impl PlatformSupplier for RustSupplier {
    fn get(&self) -> Vec<u8> {
        get_runtime().block_on(async {
            let f = &self.supplier;
            f().await
        })
    }

    fn unique_id(&self) -> &str {
        &self.unique_id
    }
}
