// automatically generated by the FlatBuffers compiler, do not modify


// @generated

use core::mem;
use core::cmp::Ordering;

extern crate flatbuffers;
use self::flatbuffers::{EndianScalar, Follow};

#[allow(unused_imports, dead_code)]
pub mod com {

  use core::mem;
  use core::cmp::Ordering;

  extern crate flatbuffers;
  use self::flatbuffers::{EndianScalar, Follow};
#[allow(unused_imports, dead_code)]
pub mod haier {

  use core::mem;
  use core::cmp::Ordering;

  extern crate flatbuffers;
  use self::flatbuffers::{EndianScalar, Follow};
#[allow(unused_imports, dead_code)]
pub mod uhome {

  use core::mem;
  use core::cmp::Ordering;

  extern crate flatbuffers;
  use self::flatbuffers::{EndianScalar, Follow};
#[allow(unused_imports, dead_code)]
pub mod uplus {

  use core::mem;
  use core::cmp::Ordering;

  extern crate flatbuffers;
  use self::flatbuffers::{EndianScalar, Follow};
#[allow(unused_imports, dead_code)]
pub mod rust {

  use core::mem;
  use core::cmp::Ordering;

  extern crate flatbuffers;
  use self::flatbuffers::{EndianScalar, Follow};
#[allow(unused_imports, dead_code)]
pub mod security {

  use core::mem;
  use core::cmp::Ordering;

  extern crate flatbuffers;
  use self::flatbuffers::{EndianScalar, Follow};
#[allow(unused_imports, dead_code)]
pub mod fbs {

  use core::mem;
  use core::cmp::Ordering;

  extern crate flatbuffers;
  use self::flatbuffers::{EndianScalar, Follow};

#[deprecated(since = "2.0.0", note = "Use associated constants instead. This will no longer be generated in 2021.")]
pub const ENUM_MIN_SECURITY_CONTAINER: u8 = 0;
#[deprecated(since = "2.0.0", note = "Use associated constants instead. This will no longer be generated in 2021.")]
pub const ENUM_MAX_SECURITY_CONTAINER: u8 = 2;
#[deprecated(since = "2.0.0", note = "Use associated constants instead. This will no longer be generated in 2021.")]
#[allow(non_camel_case_types)]
pub const ENUM_VALUES_SECURITY_CONTAINER: [SecurityContainer; 3] = [
  SecurityContainer::NONE,
  SecurityContainer::NoneWrapper,
  SecurityContainer::StrWrapper,
];

#[derive(Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Hash, Default)]
#[repr(transparent)]
pub struct SecurityContainer(pub u8);
#[allow(non_upper_case_globals)]
impl SecurityContainer {
  pub const NONE: Self = Self(0);
  pub const NoneWrapper: Self = Self(1);
  pub const StrWrapper: Self = Self(2);

  pub const ENUM_MIN: u8 = 0;
  pub const ENUM_MAX: u8 = 2;
  pub const ENUM_VALUES: &'static [Self] = &[
    Self::NONE,
    Self::NoneWrapper,
    Self::StrWrapper,
  ];
  /// Returns the variant's name or "" if unknown.
  pub fn variant_name(self) -> Option<&'static str> {
    match self {
      Self::NONE => Some("NONE"),
      Self::NoneWrapper => Some("NoneWrapper"),
      Self::StrWrapper => Some("StrWrapper"),
      _ => None,
    }
  }
}
impl core::fmt::Debug for SecurityContainer {
  fn fmt(&self, f: &mut core::fmt::Formatter) -> core::fmt::Result {
    if let Some(name) = self.variant_name() {
      f.write_str(name)
    } else {
      f.write_fmt(format_args!("<UNKNOWN {:?}>", self.0))
    }
  }
}
impl<'a> flatbuffers::Follow<'a> for SecurityContainer {
  type Inner = Self;
  #[inline]
  unsafe fn follow(buf: &'a [u8], loc: usize) -> Self::Inner {
    let b = flatbuffers::read_scalar_at::<u8>(buf, loc);
    Self(b)
  }
}

impl flatbuffers::Push for SecurityContainer {
    type Output = SecurityContainer;
    #[inline]
    unsafe fn push(&self, dst: &mut [u8], _written_len: usize) {
        flatbuffers::emplace_scalar::<u8>(dst, self.0);
    }
}

impl flatbuffers::EndianScalar for SecurityContainer {
  type Scalar = u8;
  #[inline]
  fn to_little_endian(self) -> u8 {
    self.0.to_le()
  }
  #[inline]
  #[allow(clippy::wrong_self_convention)]
  fn from_little_endian(v: u8) -> Self {
    let b = u8::from_le(v);
    Self(b)
  }
}

impl<'a> flatbuffers::Verifiable for SecurityContainer {
  #[inline]
  fn run_verifier(
    v: &mut flatbuffers::Verifier, pos: usize
  ) -> Result<(), flatbuffers::InvalidFlatbuffer> {
    use self::flatbuffers::Verifiable;
    u8::run_verifier(v, pos)
  }
}

impl flatbuffers::SimpleToVerifyInSlice for SecurityContainer {}
pub struct SecurityContainerUnionTableOffset {}

pub enum NoneWrapperOffset {}
#[derive(Copy, Clone, PartialEq)]

pub struct NoneWrapper<'a> {
  pub _tab: flatbuffers::Table<'a>,
}

impl<'a> flatbuffers::Follow<'a> for NoneWrapper<'a> {
  type Inner = NoneWrapper<'a>;
  #[inline]
  unsafe fn follow(buf: &'a [u8], loc: usize) -> Self::Inner {
    Self { _tab: flatbuffers::Table::new(buf, loc) }
  }
}

impl<'a> NoneWrapper<'a> {

  #[inline]
  pub unsafe fn init_from_table(table: flatbuffers::Table<'a>) -> Self {
    NoneWrapper { _tab: table }
  }
  #[allow(unused_mut)]
  pub fn create<'bldr: 'args, 'args: 'mut_bldr, 'mut_bldr, A: flatbuffers::Allocator + 'bldr>(
    _fbb: &'mut_bldr mut flatbuffers::FlatBufferBuilder<'bldr, A>,
    _args: &'args NoneWrapperArgs
  ) -> flatbuffers::WIPOffset<NoneWrapper<'bldr>> {
    let mut builder = NoneWrapperBuilder::new(_fbb);
    builder.finish()
  }

}

impl flatbuffers::Verifiable for NoneWrapper<'_> {
  #[inline]
  fn run_verifier(
    v: &mut flatbuffers::Verifier, pos: usize
  ) -> Result<(), flatbuffers::InvalidFlatbuffer> {
    use self::flatbuffers::Verifiable;
    v.visit_table(pos)?
     .finish();
    Ok(())
  }
}
pub struct NoneWrapperArgs {
}
impl<'a> Default for NoneWrapperArgs {
  #[inline]
  fn default() -> Self {
    NoneWrapperArgs {
    }
  }
}

pub struct NoneWrapperBuilder<'a: 'b, 'b, A: flatbuffers::Allocator + 'a> {
  fbb_: &'b mut flatbuffers::FlatBufferBuilder<'a, A>,
  start_: flatbuffers::WIPOffset<flatbuffers::TableUnfinishedWIPOffset>,
}
impl<'a: 'b, 'b, A: flatbuffers::Allocator + 'a> NoneWrapperBuilder<'a, 'b, A> {
  #[inline]
  pub fn new(_fbb: &'b mut flatbuffers::FlatBufferBuilder<'a, A>) -> NoneWrapperBuilder<'a, 'b, A> {
    let start = _fbb.start_table();
    NoneWrapperBuilder {
      fbb_: _fbb,
      start_: start,
    }
  }
  #[inline]
  pub fn finish(self) -> flatbuffers::WIPOffset<NoneWrapper<'a>> {
    let o = self.fbb_.end_table(self.start_);
    flatbuffers::WIPOffset::new(o.value())
  }
}

impl core::fmt::Debug for NoneWrapper<'_> {
  fn fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result {
    let mut ds = f.debug_struct("NoneWrapper");
      ds.finish()
  }
}
pub enum StrWrapperOffset {}
#[derive(Copy, Clone, PartialEq)]

pub struct StrWrapper<'a> {
  pub _tab: flatbuffers::Table<'a>,
}

impl<'a> flatbuffers::Follow<'a> for StrWrapper<'a> {
  type Inner = StrWrapper<'a>;
  #[inline]
  unsafe fn follow(buf: &'a [u8], loc: usize) -> Self::Inner {
    Self { _tab: flatbuffers::Table::new(buf, loc) }
  }
}

impl<'a> StrWrapper<'a> {
  pub const VT_VALUE: flatbuffers::VOffsetT = 4;

  #[inline]
  pub unsafe fn init_from_table(table: flatbuffers::Table<'a>) -> Self {
    StrWrapper { _tab: table }
  }
  #[allow(unused_mut)]
  pub fn create<'bldr: 'args, 'args: 'mut_bldr, 'mut_bldr, A: flatbuffers::Allocator + 'bldr>(
    _fbb: &'mut_bldr mut flatbuffers::FlatBufferBuilder<'bldr, A>,
    args: &'args StrWrapperArgs<'args>
  ) -> flatbuffers::WIPOffset<StrWrapper<'bldr>> {
    let mut builder = StrWrapperBuilder::new(_fbb);
    if let Some(x) = args.value { builder.add_value(x); }
    builder.finish()
  }


  #[inline]
  pub fn value(&self) -> Option<&'a str> {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<flatbuffers::ForwardsUOffset<&str>>(StrWrapper::VT_VALUE, None)}
  }
}

impl flatbuffers::Verifiable for StrWrapper<'_> {
  #[inline]
  fn run_verifier(
    v: &mut flatbuffers::Verifier, pos: usize
  ) -> Result<(), flatbuffers::InvalidFlatbuffer> {
    use self::flatbuffers::Verifiable;
    v.visit_table(pos)?
     .visit_field::<flatbuffers::ForwardsUOffset<&str>>("value", Self::VT_VALUE, false)?
     .finish();
    Ok(())
  }
}
pub struct StrWrapperArgs<'a> {
    pub value: Option<flatbuffers::WIPOffset<&'a str>>,
}
impl<'a> Default for StrWrapperArgs<'a> {
  #[inline]
  fn default() -> Self {
    StrWrapperArgs {
      value: None,
    }
  }
}

pub struct StrWrapperBuilder<'a: 'b, 'b, A: flatbuffers::Allocator + 'a> {
  fbb_: &'b mut flatbuffers::FlatBufferBuilder<'a, A>,
  start_: flatbuffers::WIPOffset<flatbuffers::TableUnfinishedWIPOffset>,
}
impl<'a: 'b, 'b, A: flatbuffers::Allocator + 'a> StrWrapperBuilder<'a, 'b, A> {
  #[inline]
  pub fn add_value(&mut self, value: flatbuffers::WIPOffset<&'b  str>) {
    self.fbb_.push_slot_always::<flatbuffers::WIPOffset<_>>(StrWrapper::VT_VALUE, value);
  }
  #[inline]
  pub fn new(_fbb: &'b mut flatbuffers::FlatBufferBuilder<'a, A>) -> StrWrapperBuilder<'a, 'b, A> {
    let start = _fbb.start_table();
    StrWrapperBuilder {
      fbb_: _fbb,
      start_: start,
    }
  }
  #[inline]
  pub fn finish(self) -> flatbuffers::WIPOffset<StrWrapper<'a>> {
    let o = self.fbb_.end_table(self.start_);
    flatbuffers::WIPOffset::new(o.value())
  }
}

impl core::fmt::Debug for StrWrapper<'_> {
  fn fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result {
    let mut ds = f.debug_struct("StrWrapper");
      ds.field("value", &self.value());
      ds.finish()
  }
}
pub enum SecurityFlatOffset {}
#[derive(Copy, Clone, PartialEq)]

pub struct SecurityFlat<'a> {
  pub _tab: flatbuffers::Table<'a>,
}

impl<'a> flatbuffers::Follow<'a> for SecurityFlat<'a> {
  type Inner = SecurityFlat<'a>;
  #[inline]
  unsafe fn follow(buf: &'a [u8], loc: usize) -> Self::Inner {
    Self { _tab: flatbuffers::Table::new(buf, loc) }
  }
}

impl<'a> SecurityFlat<'a> {
  pub const VT_CONTAINER_TYPE: flatbuffers::VOffsetT = 4;
  pub const VT_CONTAINER: flatbuffers::VOffsetT = 6;
  pub const VT_CODE: flatbuffers::VOffsetT = 8;
  pub const VT_ERROR: flatbuffers::VOffsetT = 10;

  #[inline]
  pub unsafe fn init_from_table(table: flatbuffers::Table<'a>) -> Self {
    SecurityFlat { _tab: table }
  }
  #[allow(unused_mut)]
  pub fn create<'bldr: 'args, 'args: 'mut_bldr, 'mut_bldr, A: flatbuffers::Allocator + 'bldr>(
    _fbb: &'mut_bldr mut flatbuffers::FlatBufferBuilder<'bldr, A>,
    args: &'args SecurityFlatArgs<'args>
  ) -> flatbuffers::WIPOffset<SecurityFlat<'bldr>> {
    let mut builder = SecurityFlatBuilder::new(_fbb);
    if let Some(x) = args.error { builder.add_error(x); }
    builder.add_code(args.code);
    if let Some(x) = args.container { builder.add_container(x); }
    builder.add_container_type(args.container_type);
    builder.finish()
  }


  #[inline]
  pub fn container_type(&self) -> SecurityContainer {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<SecurityContainer>(SecurityFlat::VT_CONTAINER_TYPE, Some(SecurityContainer::NONE)).unwrap()}
  }
  #[inline]
  pub fn container(&self) -> Option<flatbuffers::Table<'a>> {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<flatbuffers::ForwardsUOffset<flatbuffers::Table<'a>>>(SecurityFlat::VT_CONTAINER, None)}
  }
  #[inline]
  pub fn code(&self) -> i32 {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<i32>(SecurityFlat::VT_CODE, Some(0)).unwrap()}
  }
  #[inline]
  pub fn error(&self) -> Option<&'a str> {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<flatbuffers::ForwardsUOffset<&str>>(SecurityFlat::VT_ERROR, None)}
  }
  #[inline]
  #[allow(non_snake_case)]
  pub fn container_as_none_wrapper(&self) -> Option<NoneWrapper<'a>> {
    if self.container_type() == SecurityContainer::NoneWrapper {
      self.container().map(|t| {
       // Safety:
       // Created from a valid Table for this object
       // Which contains a valid union in this slot
       unsafe { NoneWrapper::init_from_table(t) }
     })
    } else {
      None
    }
  }

  #[inline]
  #[allow(non_snake_case)]
  pub fn container_as_str_wrapper(&self) -> Option<StrWrapper<'a>> {
    if self.container_type() == SecurityContainer::StrWrapper {
      self.container().map(|t| {
       // Safety:
       // Created from a valid Table for this object
       // Which contains a valid union in this slot
       unsafe { StrWrapper::init_from_table(t) }
     })
    } else {
      None
    }
  }

}

impl flatbuffers::Verifiable for SecurityFlat<'_> {
  #[inline]
  fn run_verifier(
    v: &mut flatbuffers::Verifier, pos: usize
  ) -> Result<(), flatbuffers::InvalidFlatbuffer> {
    use self::flatbuffers::Verifiable;
    v.visit_table(pos)?
     .visit_union::<SecurityContainer, _>("container_type", Self::VT_CONTAINER_TYPE, "container", Self::VT_CONTAINER, false, |key, v, pos| {
        match key {
          SecurityContainer::NoneWrapper => v.verify_union_variant::<flatbuffers::ForwardsUOffset<NoneWrapper>>("SecurityContainer::NoneWrapper", pos),
          SecurityContainer::StrWrapper => v.verify_union_variant::<flatbuffers::ForwardsUOffset<StrWrapper>>("SecurityContainer::StrWrapper", pos),
          _ => Ok(()),
        }
     })?
     .visit_field::<i32>("code", Self::VT_CODE, false)?
     .visit_field::<flatbuffers::ForwardsUOffset<&str>>("error", Self::VT_ERROR, false)?
     .finish();
    Ok(())
  }
}
pub struct SecurityFlatArgs<'a> {
    pub container_type: SecurityContainer,
    pub container: Option<flatbuffers::WIPOffset<flatbuffers::UnionWIPOffset>>,
    pub code: i32,
    pub error: Option<flatbuffers::WIPOffset<&'a str>>,
}
impl<'a> Default for SecurityFlatArgs<'a> {
  #[inline]
  fn default() -> Self {
    SecurityFlatArgs {
      container_type: SecurityContainer::NONE,
      container: None,
      code: 0,
      error: None,
    }
  }
}

pub struct SecurityFlatBuilder<'a: 'b, 'b, A: flatbuffers::Allocator + 'a> {
  fbb_: &'b mut flatbuffers::FlatBufferBuilder<'a, A>,
  start_: flatbuffers::WIPOffset<flatbuffers::TableUnfinishedWIPOffset>,
}
impl<'a: 'b, 'b, A: flatbuffers::Allocator + 'a> SecurityFlatBuilder<'a, 'b, A> {
  #[inline]
  pub fn add_container_type(&mut self, container_type: SecurityContainer) {
    self.fbb_.push_slot::<SecurityContainer>(SecurityFlat::VT_CONTAINER_TYPE, container_type, SecurityContainer::NONE);
  }
  #[inline]
  pub fn add_container(&mut self, container: flatbuffers::WIPOffset<flatbuffers::UnionWIPOffset>) {
    self.fbb_.push_slot_always::<flatbuffers::WIPOffset<_>>(SecurityFlat::VT_CONTAINER, container);
  }
  #[inline]
  pub fn add_code(&mut self, code: i32) {
    self.fbb_.push_slot::<i32>(SecurityFlat::VT_CODE, code, 0);
  }
  #[inline]
  pub fn add_error(&mut self, error: flatbuffers::WIPOffset<&'b  str>) {
    self.fbb_.push_slot_always::<flatbuffers::WIPOffset<_>>(SecurityFlat::VT_ERROR, error);
  }
  #[inline]
  pub fn new(_fbb: &'b mut flatbuffers::FlatBufferBuilder<'a, A>) -> SecurityFlatBuilder<'a, 'b, A> {
    let start = _fbb.start_table();
    SecurityFlatBuilder {
      fbb_: _fbb,
      start_: start,
    }
  }
  #[inline]
  pub fn finish(self) -> flatbuffers::WIPOffset<SecurityFlat<'a>> {
    let o = self.fbb_.end_table(self.start_);
    flatbuffers::WIPOffset::new(o.value())
  }
}

impl core::fmt::Debug for SecurityFlat<'_> {
  fn fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result {
    let mut ds = f.debug_struct("SecurityFlat");
      ds.field("container_type", &self.container_type());
      match self.container_type() {
        SecurityContainer::NoneWrapper => {
          if let Some(x) = self.container_as_none_wrapper() {
            ds.field("container", &x)
          } else {
            ds.field("container", &"InvalidFlatbuffer: Union discriminant does not match value.")
          }
        },
        SecurityContainer::StrWrapper => {
          if let Some(x) = self.container_as_str_wrapper() {
            ds.field("container", &x)
          } else {
            ds.field("container", &"InvalidFlatbuffer: Union discriminant does not match value.")
          }
        },
        _ => {
          let x: Option<()> = None;
          ds.field("container", &x)
        },
      };
      ds.field("code", &self.code());
      ds.field("error", &self.error());
      ds.finish()
  }
}
#[inline]
/// Verifies that a buffer of bytes contains a `SecurityFlat`
/// and returns it.
/// Note that verification is still experimental and may not
/// catch every error, or be maximally performant. For the
/// previous, unchecked, behavior use
/// `root_as_security_flat_unchecked`.
pub fn root_as_security_flat(buf: &[u8]) -> Result<SecurityFlat, flatbuffers::InvalidFlatbuffer> {
  flatbuffers::root::<SecurityFlat>(buf)
}
#[inline]
/// Verifies that a buffer of bytes contains a size prefixed
/// `SecurityFlat` and returns it.
/// Note that verification is still experimental and may not
/// catch every error, or be maximally performant. For the
/// previous, unchecked, behavior use
/// `size_prefixed_root_as_security_flat_unchecked`.
pub fn size_prefixed_root_as_security_flat(buf: &[u8]) -> Result<SecurityFlat, flatbuffers::InvalidFlatbuffer> {
  flatbuffers::size_prefixed_root::<SecurityFlat>(buf)
}
#[inline]
/// Verifies, with the given options, that a buffer of bytes
/// contains a `SecurityFlat` and returns it.
/// Note that verification is still experimental and may not
/// catch every error, or be maximally performant. For the
/// previous, unchecked, behavior use
/// `root_as_security_flat_unchecked`.
pub fn root_as_security_flat_with_opts<'b, 'o>(
  opts: &'o flatbuffers::VerifierOptions,
  buf: &'b [u8],
) -> Result<SecurityFlat<'b>, flatbuffers::InvalidFlatbuffer> {
  flatbuffers::root_with_opts::<SecurityFlat<'b>>(opts, buf)
}
#[inline]
/// Verifies, with the given verifier options, that a buffer of
/// bytes contains a size prefixed `SecurityFlat` and returns
/// it. Note that verification is still experimental and may not
/// catch every error, or be maximally performant. For the
/// previous, unchecked, behavior use
/// `root_as_security_flat_unchecked`.
pub fn size_prefixed_root_as_security_flat_with_opts<'b, 'o>(
  opts: &'o flatbuffers::VerifierOptions,
  buf: &'b [u8],
) -> Result<SecurityFlat<'b>, flatbuffers::InvalidFlatbuffer> {
  flatbuffers::size_prefixed_root_with_opts::<SecurityFlat<'b>>(opts, buf)
}
#[inline]
/// Assumes, without verification, that a buffer of bytes contains a SecurityFlat and returns it.
/// # Safety
/// Callers must trust the given bytes do indeed contain a valid `SecurityFlat`.
pub unsafe fn root_as_security_flat_unchecked(buf: &[u8]) -> SecurityFlat {
  flatbuffers::root_unchecked::<SecurityFlat>(buf)
}
#[inline]
/// Assumes, without verification, that a buffer of bytes contains a size prefixed SecurityFlat and returns it.
/// # Safety
/// Callers must trust the given bytes do indeed contain a valid size prefixed `SecurityFlat`.
pub unsafe fn size_prefixed_root_as_security_flat_unchecked(buf: &[u8]) -> SecurityFlat {
  flatbuffers::size_prefixed_root_unchecked::<SecurityFlat>(buf)
}
#[inline]
pub fn finish_security_flat_buffer<'a, 'b, A: flatbuffers::Allocator + 'a>(
    fbb: &'b mut flatbuffers::FlatBufferBuilder<'a, A>,
    root: flatbuffers::WIPOffset<SecurityFlat<'a>>) {
  fbb.finish(root, None);
}

#[inline]
pub fn finish_size_prefixed_security_flat_buffer<'a, 'b, A: flatbuffers::Allocator + 'a>(fbb: &'b mut flatbuffers::FlatBufferBuilder<'a, A>, root: flatbuffers::WIPOffset<SecurityFlat<'a>>) {
  fbb.finish_size_prefixed(root, None);
}
}  // pub mod fbs
}  // pub mod security
}  // pub mod rust
}  // pub mod uplus
}  // pub mod uhome
}  // pub mod haier
}  // pub mod com

