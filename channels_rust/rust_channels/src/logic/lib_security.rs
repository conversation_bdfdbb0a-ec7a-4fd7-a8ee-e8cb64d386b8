use crate::logic::private_key::*;
use crate::logic::public_key::*;
use crate::logic::security_generated::com::haier::uhome::uplus::rust::security::fbs::{
    NoneWrapper, NoneWrapperArgs, SecurityContainer, SecurityFlat, SecurityFlatArgs, Str<PERSON>rapper,
    StrWrapperArgs,
};
use base64::engine::general_purpose;
use base64::{DecodeError, Engine};
use flatbuffers::FlatBufferBuilder;
use log::{debug, error, warn};
use once_cell::sync::OnceCell;
use openssl::error::ErrorStack;
use openssl::pkey::{Private, Public};
use openssl::rsa::{Padding, Rsa};
use openssl::symm::{Cipher, Crypter, Mode};
use parking_lot::RwLock;
use std::collections::HashMap;
use std::string::FromUtf8Error;
use thiserror::Error;

macro_rules! require_params {
    ($params:expr, $($param:expr),+) => {
        for &param in &[$($param),+] {
            if !$params.contains_key(param) {
                warn!("Required parameter '{}' is missing", param);
                return invalid_arg_result(&format!("{} is required", param));
            }
        }
    };
}

pub fn lib_security_cross_platform(params: HashMap<String, String>) -> Vec<u8> {
    let action = params.get("action").unwrap_or(&EMPTY).as_str();
    debug!("lib_security executing action: {}", action);
    match action {
        "init" => init(),
        "rsa_pub_encrypt" => rsa_pub_encrypt(params),
        "rsa_pri_decrypt" => rsa_pri_decrypt(params),
        "set_aes_key" => set_aes_key(params),
        "aes_encrypt" => aes_encrypt(params),
        "aes_decrypt" => aes_decrypt(params),
        _ => {
            warn!("lib_security executing action invalid: {}", action);
            Vec::new()
        }
    }
}

fn init() -> Vec<u8> {
    #[cfg(feature = "android")]
    {
        crate::features::android::init_logger();
    }
    #[cfg(feature = "ios")]
    {}
    Vec::new()
}

fn rsa_pub_encrypt(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, CONTENT);
    let content = params.get(CONTENT).unwrap().to_string();
    let read = get_security().read();
    match read.encrypt(content) {
        Ok(it) => string_result(CODE_OK, it.as_str()),
        Err(err) => failure_result(CODE_RSA_ERROR, err.to_string().as_str()),
    }
}

fn rsa_pri_decrypt(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, CONTENT);
    let content = params.get(CONTENT).unwrap().to_string();
    let read = get_security().read();
    match read.decrypt(content) {
        Ok(it) => string_result(CODE_OK, it.as_str()),
        Err(err) => failure_result(CODE_RSA_ERROR, err.to_string().as_str()),
    }
}

fn set_aes_key(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, CONTENT);
    let content = params.get(CONTENT).unwrap().to_string();
    let mut write = get_security().write();
    match write.set_aes_key(content) {
        Ok(_) => string_result(CODE_OK, EMPTY.as_str()),
        Err(err) => failure_result(CODE_RSA_ERROR, err.to_string().as_str()),
    }
}

fn aes_encrypt(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, IV, CONTENT);
    let iv = params.get(IV).unwrap().to_string();
    let content = params.get(CONTENT).unwrap().to_string();
    let read = get_security().read();
    match read.aes_encrypt(iv, content) {
        Ok(it) => string_result(CODE_OK, it.as_str()),
        Err(err) => failure_result(CODE_RSA_ERROR, err.to_string().as_str()),
    }
}

fn aes_decrypt(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, IV, CONTENT);
    let iv = params.get(IV).unwrap().to_string();
    let content = params.get(CONTENT).unwrap().to_string();
    let read = get_security().read();
    match read.aes_decrypt(iv, content) {
        Ok(it) => string_result(CODE_OK, it.as_str()),
        Err(err) => failure_result(CODE_RSA_ERROR, err.to_string().as_str()),
    }
}

fn invalid_arg_result(info: &str) -> Vec<u8> {
    failure_result(CODE_INVALID_PARAM, info)
}

fn failure_result(code: i32, info: &str) -> Vec<u8> {
    error!("lib_security ffi error msg: {}, {}", code, info);
    let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
    let error_info = builder.create_string(info);
    let none_wrapper = NoneWrapper::create(&mut builder, &NoneWrapperArgs {});
    let flat = SecurityFlat::create(
        &mut builder,
        &SecurityFlatArgs {
            container_type: SecurityContainer::NoneWrapper,
            container: Some(none_wrapper.as_union_value()),
            code,
            error: Some(error_info),
        },
    );
    builder.finish(flat, None);
    builder.finished_data().to_vec()
}

fn string_result(code: i32, info: &str) -> Vec<u8> {
    let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
    let ok_info = builder.create_string(info);
    let str_wrapper = StrWrapper::create(
        &mut builder,
        &StrWrapperArgs {
            value: Some(ok_info),
        },
    );
    let flat = SecurityFlat::create(
        &mut builder,
        &SecurityFlatArgs {
            container_type: SecurityContainer::StrWrapper,
            container: Some(str_wrapper.as_union_value()),
            code,
            error: None,
        },
    );
    builder.finish(flat, None);
    builder.finished_data().to_vec()
}

const DEFAULT_SIZE: usize = 1024;
const CODE_OK: i32 = 0;
const CODE_INVALID_PARAM: i32 = 10000;
const CODE_RSA_ERROR: i32 = 10001;
static EMPTY: String = String::new();
static CONTENT: &str = "content";
static IV: &str = "iv";
static SECURITY: OnceCell<RwLock<Security>> = OnceCell::new();

fn get_security() -> &'static RwLock<Security> {
    SECURITY.get_or_init(|| RwLock::new(Security::new()))
}

struct Security {
    private_key: Rsa<Private>,
    public_key: Rsa<Public>,
    aes_key: Option<Vec<u8>>,
    cipher: Cipher,
}

impl Security {
    fn new() -> Security {
        let private_key = Rsa::private_key_from_pem(assemble_priv_key_pem().as_bytes()).unwrap();
        let public_key = Rsa::public_key_from_pem(assemble_pub_key_pem().as_bytes()).unwrap();
        Security {
            private_key,
            public_key,
            aes_key: None,
            cipher: Cipher::aes_256_cbc(),
        }
    }

    fn encrypt(&self, data: String) -> Result<String, SecurityError> {
        let mut encrypted = vec![0; self.public_key.size() as usize];
        let encrypted_len =
            self.public_key
                .public_encrypt(data.as_bytes(), &mut encrypted, Padding::PKCS1_OAEP)?;
        encrypted.truncate(encrypted_len);
        Ok(general_purpose::STANDARD.encode(&encrypted))
    }

    fn decrypt(&self, data: String) -> Result<String, SecurityError> {
        let mut decrypted = vec![0; self.private_key.size() as usize];
        let encrypted_bytes = general_purpose::STANDARD.decode(&data)?;
        let decrypted_len = self.private_key.private_decrypt(
            &encrypted_bytes,
            &mut decrypted,
            Padding::PKCS1_OAEP,
        )?;
        decrypted.truncate(decrypted_len);
        Ok(String::from_utf8(decrypted)?)
    }

    fn set_aes_key(&mut self, key: String) -> Result<(), SecurityError> {
        let mut decrypted = vec![0; self.private_key.size() as usize];
        let key_bytes = general_purpose::STANDARD.decode(&key)?;
        let decrypted_len =
            self.private_key
                .private_decrypt(&key_bytes, &mut decrypted, Padding::PKCS1_OAEP)?;
        decrypted.truncate(decrypted_len);
        let key = general_purpose::STANDARD.decode(&decrypted)?;
        self.aes_key = Some(key);
        Ok(())
    }

    fn aes_encrypt(&self, iv: String, plaintext: String) -> Result<String, SecurityError> {
        let iv_bytes = general_purpose::STANDARD.decode(&iv)?;
        if self.aes_key.is_none() {
            return Err(SecurityError::ExchangeError);
        }
        let key = self.aes_key.clone().unwrap();
        let mut crypter = Crypter::new(self.cipher, Mode::Encrypt, &key, Some(&iv_bytes))?;
        let data = plaintext.as_bytes();
        let mut ciphertext = vec![0; data.len() + self.cipher.block_size()];
        let mut count = crypter.update(data, &mut ciphertext)?;
        count += crypter.finalize(&mut ciphertext[count..])?;
        ciphertext.truncate(count);
        Ok(general_purpose::STANDARD.encode(&ciphertext))
    }

    pub fn aes_decrypt(&self, iv: String, text: String) -> Result<String, SecurityError> {
        let iv_bytes = general_purpose::STANDARD.decode(&iv)?;
        if self.aes_key.is_none() {
            return Err(SecurityError::ExchangeError);
        }
        let key = self.aes_key.clone().unwrap();
        let mut crypter = Crypter::new(self.cipher, Mode::Decrypt, &key, Some(&iv_bytes))?;
        let data = general_purpose::STANDARD.decode(&text)?;
        let mut ciphertext = vec![0; data.len() + self.cipher.block_size()];
        let mut count = crypter.update(&data, &mut ciphertext)?;
        count += crypter.finalize(&mut ciphertext[count..])?;
        ciphertext.truncate(count);
        Ok(String::from_utf8(ciphertext)?)
    }
}

#[derive(Error, Debug)]
enum SecurityError {
    #[error("lib_security: OpensslErrorStack: {0}")]
    OpensslErrorStack(#[from] ErrorStack),
    #[error("lib_security: FromUtf8Error: {0}")]
    FromUtf8Error(#[from] FromUtf8Error),
    #[error("lib_security: base64 DecodeError: {0}")]
    DecodeError(#[from] DecodeError),
    #[error("lib_security: You must first use RSA to exchange the AES encryption key")]
    ExchangeError,
}

fn assemble_pub_key_pem() -> String {
    let pub_segments = &[
        PUB_SEG_START,
        PUB_SEG_1,
        PUB_SEG_2,
        PUB_SEG_3,
        PUB_SEG_4,
        PUB_SEG_5,
        PUB_SEG_6,
        PUB_SEG_7,
        PUB_SEG_END,
    ];
    pub_segments.join("\n") + "\n"
}

fn assemble_priv_key_pem() -> String {
    let pri_segments = &[
        PRI_SEG_START,
        PRI_SEG_1,
        PRI_SEG_2,
        PRI_SEG_3,
        PRI_SEG_4,
        PRI_SEG_5,
        PRI_SEG_6,
        PRI_SEG_7,
        PRI_SEG_8,
        PRI_SEG_9,
        PRI_SEG_10,
        PRI_SEG_11,
        PRI_SEG_12,
        PRI_SEG_13,
        PRI_SEG_14,
        PRI_SEG_15,
        PRI_SEG_16,
        PRI_SEG_17,
        PRI_SEG_18,
        PRI_SEG_19,
        PRI_SEG_20,
        PRI_SEG_21,
        PRI_SEG_22,
        PRI_SEG_23,
        PRI_SEG_24,
        PRI_SEG_25,
        PRI_SEG_26,
        PRI_SEG_END,
    ];
    pri_segments.join("\n") + "\n"
}
