[package]
name = "rust_channels"
version = "0.1.0"
edition = "2021"

[lib]
name = "rust_channels"
crate-type = ["cdylib", "staticlib", "rlib"]

[features]
default = []
android = ["dep:jni", "dep:android_logger", "task_manager/android"]
ios = []
ohos = []

[dependencies]
jni = { version = "0.21.1", optional = true }
android_logger = { version = "0.14.1", optional = true }
flutter_rust_bridge = "=2.5.0"
flatbuffers = "=24.3.25"
log = "0.4.22"
parking_lot = "0.12.3"
thiserror = "1.0"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
openssl = "0.10.68"
base64 = "0.22"
once_cell = "1.20"
task_manager = { version = "0.1.0-2024092501", registry = "uplus-registry" }

[build-dependencies]
napi-build-ohos = "1.0"
cc = "1.1"
