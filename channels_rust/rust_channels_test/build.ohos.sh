#!/bin/bash

# 本地检查：cargo clippy --no-deps -- -D clippy::all -D warnings -A dead_code -A unused-variables
# 自动修复：cargo clippy --fix --no-deps -- -D clippy::all -D warnings -A dead_code -A unused-variables


PWD=$(pwd)
ARCHS=("ohos-arm64-v8a")

for arch in "${ARCHS[@]}"; do
  case $arch in
    "ohos-arm64-v8a")
      TARGET_HOST=aarch64-unknown-linux-ohos
      ARCH_DIR="arm64-v8a"
      ;;
    "ohos-armeabi-v7a")
      TARGET_HOST=armv7-unknown-linux-ohos
      ARCH_DIR="armeabi-v7a"
      ;;
  esac

  export CC="$OH_NDK_TOOLCHAIN/bin/$TARGET_HOST-clang"
  cargo build --target "$TARGET_HOST" --features ohos --target-dir target
#  cp -a target/"$TARGET_HOST"/debug/librust_uplus.so ../ohosApp/entry/libs/"$ARCH_DIR"

done
