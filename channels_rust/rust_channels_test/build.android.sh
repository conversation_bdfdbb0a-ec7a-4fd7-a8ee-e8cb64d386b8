#!/bin/bash

PWD=$(pwd)
ARCHS=("arm64-v8a" "armeabi-v7a")

for arch in "${ARCHS[@]}"; do
  case $arch in
    "arm64-v8a")
      TARGET_HOST=aarch64-linux-android
      ;;
    "armeabi-v7a")
      TARGET_HOST=armv7-linux-androideabi
      ;;
  esac

  # 配置为自己本地的路径
  export OPENSSL_DIR=$HOME/lib/rust/env/$arch/openssl
  export OPENSSL_LIB_DIR=$OPENSSL_DIR/lib
  export OPENSSL_INCLUDE_DIR=$OPENSSL_DIR/include
  cargo ndk -t "$TARGET_HOST" build --features android --release --target-dir target

done
