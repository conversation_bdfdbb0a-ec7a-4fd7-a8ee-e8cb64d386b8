#!/usr/bin/env python3

import sys
import os
import struct
from google.protobuf.message import DecodeError

# 尝试导入protobuf
try:
    import google.protobuf
    from google.protobuf import message
    print(f"✅ Using protobuf version: {google.protobuf.__version__}")
except ImportError:
    print("❌ Error: protobuf not installed. Install with: pip install protobuf")
    sys.exit(1)

def create_log_metadata_class():
    """动态创建LogMetaData类，因为我们没有编译好的Python protobuf文件"""
    from google.protobuf import descriptor
    from google.protobuf import message
    from google.protobuf import reflection
    from google.protobuf import symbol_database
    
    # 创建字段描述符
    fields = [
        descriptor.FieldDescriptor(
            name='time', full_name='logger.LogMetaData.time',
            index=0, number=1, type=9, cpp_type=9, label=1,
            has_default_value=False, default_value=b"".decode('utf-8'),
            message_type=None, enum_type=None, containing_type=None,
            is_extension=False, extension_scope=None,
            serialized_options=None, file=None, create_key=descriptor._internal_create_key),
        descriptor.FieldDescriptor(
            name='session_id', full_name='logger.LogMetaData.session_id',
            index=1, number=2, type=9, cpp_type=9, label=1,
            has_default_value=False, default_value=b"".decode('utf-8'),
            message_type=None, enum_type=None, containing_type=None,
            is_extension=False, extension_scope=None,
            serialized_options=None, file=None, create_key=descriptor._internal_create_key),
        descriptor.FieldDescriptor(
            name='tag', full_name='logger.LogMetaData.tag',
            index=2, number=3, type=9, cpp_type=9, label=1,
            has_default_value=False, default_value=b"".decode('utf-8'),
            message_type=None, enum_type=None, containing_type=None,
            is_extension=False, extension_scope=None,
            serialized_options=None, file=None, create_key=descriptor._internal_create_key),
        descriptor.FieldDescriptor(
            name='level', full_name='logger.LogMetaData.level',
            index=3, number=4, type=9, cpp_type=9, label=1,
            has_default_value=False, default_value=b"".decode('utf-8'),
            message_type=None, enum_type=None, containing_type=None,
            is_extension=False, extension_scope=None,
            serialized_options=None, file=None, create_key=descriptor._internal_create_key),
        descriptor.FieldDescriptor(
            name='test_mode', full_name='logger.LogMetaData.test_mode',
            index=4, number=5, type=9, cpp_type=9, label=1,
            has_default_value=False, default_value=b"".decode('utf-8'),
            message_type=None, enum_type=None, containing_type=None,
            is_extension=False, extension_scope=None,
            serialized_options=None, file=None, create_key=descriptor._internal_create_key),
        descriptor.FieldDescriptor(
            name='user_id', full_name='logger.LogMetaData.user_id',
            index=5, number=6, type=9, cpp_type=9, label=1,
            has_default_value=False, default_value=b"".decode('utf-8'),
            message_type=None, enum_type=None, containing_type=None,
            is_extension=False, extension_scope=None,
            serialized_options=None, file=None, create_key=descriptor._internal_create_key),
        descriptor.FieldDescriptor(
            name='log_message', full_name='logger.LogMetaData.log_message',
            index=6, number=7, type=9, cpp_type=9, label=1,
            has_default_value=False, default_value=b"".decode('utf-8'),
            message_type=None, enum_type=None, containing_type=None,
            is_extension=False, extension_scope=None,
            serialized_options=None, file=None, create_key=descriptor._internal_create_key),
    ]
    
    # 创建消息描述符
    msg_descriptor = descriptor.Descriptor(
        name='LogMetaData',
        full_name='logger.LogMetaData',
        filename=None,
        file=None,
        containing_type=None,
        fields=fields,
        extensions=[],
        nested_types=[],
        enum_types=[],
        serialized_options=None,
        is_extendable=False,
        syntax='proto3',
        extension_ranges=[],
        oneofs=[],
        serialized_start=None,
        serialized_end=None,
        create_key=descriptor._internal_create_key)
    
    # 创建消息类
    LogMetaData = reflection.GeneratedProtocolMessageType(
        'LogMetaData',
        (message.Message,),
        {
            'DESCRIPTOR': msg_descriptor,
            '__module__': 'log_metadata_pb2'
        }
    )
    
    return LogMetaData

def parse_delimited_messages(data):
    """解析带长度前缀的protobuf消息"""
    LogMetaData = create_log_metadata_class()
    messages = []
    offset = 0
    
    while offset < len(data):
        try:
            # 读取varint长度
            length, varint_size = read_varint(data[offset:])
            if length == 0:
                break
                
            offset += varint_size
            
            # 检查是否有足够的数据
            if offset + length > len(data):
                print(f"⚠️  Warning: Not enough data for message at offset {offset}")
                break
            
            # 提取消息数据
            message_data = data[offset:offset + length]
            offset += length
            
            # 解析protobuf消息
            try:
                log_entry = LogMetaData()
                log_entry.ParseFromString(message_data)
                messages.append(log_entry)
                print(f"✅ Parsed message {len(messages)}: {log_entry.level} - {log_entry.tag}")
            except DecodeError as e:
                print(f"❌ Failed to parse protobuf message: {e}")
                # 尝试直接解析（可能没有长度前缀）
                try:
                    log_entry = LogMetaData()
                    log_entry.ParseFromString(data)
                    messages.append(log_entry)
                    print(f"✅ Parsed direct message: {log_entry.level} - {log_entry.tag}")
                    break
                except DecodeError:
                    print(f"❌ Failed to parse as direct protobuf message")
                    break
                
        except Exception as e:
            print(f"❌ Error at offset {offset}: {e}")
            break
    
    return messages

def read_varint(data):
    """读取protobuf varint"""
    result = 0
    shift = 0
    pos = 0
    
    while pos < len(data):
        byte = data[pos]
        result |= (byte & 0x7F) << shift
        pos += 1
        
        if (byte & 0x80) == 0:
            return result, pos
            
        shift += 7
        if shift >= 64:
            raise ValueError("Varint too long")
    
    raise ValueError("Incomplete varint")

def try_parse_single_message(data):
    """尝试将整个数据作为单个protobuf消息解析"""
    LogMetaData = create_log_metadata_class()
    
    try:
        log_entry = LogMetaData()
        log_entry.ParseFromString(data)
        return [log_entry]
    except DecodeError as e:
        print(f"❌ Failed to parse as single protobuf message: {e}")
        return []

def main():
    if len(sys.argv) != 2:
        print("Usage: python3 decode_protobuf_logs.py <decoded_log_file>")
        print("Example: python3 decode_protobuf_logs.py /Users/<USER>/Downloads/uplog_20250620.xlog.log")
        sys.exit(1)
    
    log_file = sys.argv[1]
    
    if not os.path.exists(log_file):
        print(f"❌ File not found: {log_file}")
        sys.exit(1)
    
    print(f"🔧 Reading protobuf data from: {log_file}")
    
    with open(log_file, 'rb') as f:
        data = f.read()
    
    print(f"📁 File size: {len(data)} bytes")
    print(f"📋 First 32 bytes: {' '.join(f'{b:02x}' for b in data[:32])}")
    
    # 尝试不同的解析方法
    print("\n🔍 Trying delimited message format...")
    messages = parse_delimited_messages(data)
    
    if not messages:
        print("\n🔍 Trying single message format...")
        messages = try_parse_single_message(data)
    
    if messages:
        print(f"\n🎉 Successfully parsed {len(messages)} log entries:")
        print("=" * 80)
        
        for i, msg in enumerate(messages, 1):
            print(f"\n📝 Log Entry #{i}:")
            print(f"   Time: {msg.time}")
            print(f"   Level: {msg.level}")
            print(f"   Tag: {msg.tag}")
            print(f"   Session ID: {msg.session_id}")
            print(f"   User ID: {msg.user_id}")
            print(f"   Test Mode: {msg.test_mode}")
            print(f"   Message: {msg.log_message}")
            print("-" * 40)
        
        # 保存为可读文本
        output_file = log_file + ".txt"
        with open(output_file, 'w', encoding='utf-8') as f:
            for i, msg in enumerate(messages, 1):
                f.write(f"Log Entry #{i}:\n")
                f.write(f"Time: {msg.time}\n")
                f.write(f"Level: {msg.level}\n")
                f.write(f"Tag: {msg.tag}\n")
                f.write(f"Session ID: {msg.session_id}\n")
                f.write(f"User ID: {msg.user_id}\n")
                f.write(f"Test Mode: {msg.test_mode}\n")
                f.write(f"Message: {msg.log_message}\n")
                f.write("-" * 40 + "\n")
        
        print(f"\n💾 Readable logs saved to: {output_file}")
    else:
        print("\n❌ No valid protobuf messages found")
        print("💡 The data might be:")
        print("   1. Not protobuf format")
        print("   2. Using a different protobuf schema")
        print("   3. Still compressed or encrypted")

if __name__ == "__main__":
    main()
