# UpLog - HarmonyOS日志库

## 🎯 库定位和职责

UpLog是HarmonyOS日志架构中的**ArkTS接口层**，作为应用层的统一入口。

### 架构说明
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     UpLog       │───▶│ logger/ohosApp  │───▶│ logger/rust_*   │
│   (前端接口)    │    │   (桥接层)      │    │   (Rust逻辑)    │
│                 │    │                 │    │                 │
│ • 配置接口      │    │ • RustChannel   │    │ • 统一日志逻辑  │
│ • API兼容       │    │ • FlatBuffer    │    │ • xlog文件管理  │
│ • 格式转换      │    │ • 类型转换      │    │ • 隐私处理      │
│ • 简单传递      │    │ • 异步调用      │    │ • protobuf格式  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 本库(UpLog)的核心职责
1. **配置管理**: 提供Builder模式的配置接口
2. **API兼容**: 保持现有API的向后兼容性
3. **格式转换**: 自动转换鸿蒙格式(%{public}s)为Rust格式({})
4. **简单传递**: userId更新、隐私状态设置等操作直接传递给下层
5. **无业务逻辑**: 不包含任何存储、格式化、上传等业务逻辑

### 相关库说明
- **logger/ohosApp**: HarmonyOS桥接层，通过RustChannel与Rust层交互，使用FlatBuffer进行数据序列化
- **logger/rust_logger**: Rust统一逻辑库，使用xlog进行文件管理，实现protobuf序列化、隐私处理、上传等功能

uplog提供日志输出能力，支持多级别日志、文件存储、隐私控制和日志上传等功能。

# 使用说明

## 1. 基础初始化
```typescript
// 简单初始化（使用默认配置）
UpLoggerInjection.initialize(context);
```

## 2. 高级初始化（推荐）
```typescript
import { UpLoggerInjection, UpLogConfig, LogLevel } from 'uplog/Index';

// 使用Builder模式创建配置
const config = UpLogConfig.builder()
  .setLogLevel(LogLevel.DEBUG)
  .setUserId("user123")
  .setPrivacyAgreed(true)
  .setFullLog(true) // 开启全量日志（会触发上传）
  .setFileConfig(20 * 1024 * 1024, 200 * 1024 * 1024) // 20MB单文件，200MB目录总大小
  .setUploadUrl("https://api.example.com/logs")
  .build();

// 使用配置初始化
UpLoggerInjection.initializeWithConfig(context, config);
```

## 3. 创建Logger
```typescript
let logger = UpLoggerInjection.provideManager().createLogger(TAG);
```

## 4. 简单更新（统一入口）
```typescript
// 用户登录后更新用户ID（通过RustChannel传递给Rust层）
UpLoggerInjection.updateUserId("newUser789");

// 设置隐私协议状态（通过RustChannel传递给Rust层）
UpLoggerInjection.setPrivacyAgreed(true);

// 启用控制台日志输出（异步执行）
UpLoggerInjection.enableConsoleLog(true);

// 启用完整日志模式（异步执行）
UpLoggerInjection.enableFullLogs(true);
```

## 5. 日志输出

支持debug、info、warn、error级别的日志输出。

```typescript
logger.debug(format: string, ...args: Object[]): void
logger.info(format: string, ...args: Object[]): void
logger.warn(format: string, ...args: Object[]): void
logger.error(format: string, ...args: Object[]): void
```

### 格式化兼容性

为保持现有代码兼容，继续支持鸿蒙格式，UpLog会自动转换：

```typescript
// 现有格式（继续支持）
logger.info("user: %{public}s, count: %{public}d", "username", 999);
logger.warn("error: %{private}s", "sensitive_data");

// 自动转换为Rust格式并传递给logger库
// %{public}s -> {}
// %{private}s -> {}
// 脱敏处理由Rust层统一处理
```

**注意**: 脱敏处理完全由Rust层统一处理，使用Android兼容的脱敏规则。数据通过RustChannel和FlatBuffer格式传递到Rust层。


## 6. 设置日志级别

```typescript
UpLoggerInjection.provideManager().setLogLevel(LogLevel.ERROR);
```
低于设置级别的日志不会在控制台输出。

设置日志级别后会进行持久化缓存，下次启动从缓存中初始化日志级别。

## 8. 调试和开发接口

### 控制台日志控制
```typescript
// 启用控制台日志输出（异步执行）
UpLoggerInjection.enableConsoleLog(true);

// 禁用控制台日志输出（异步执行）
UpLoggerInjection.enableConsoleLog(false);
```

### 完整日志模式
```typescript
// 启用完整日志模式（异步执行）
// 会自动设置日志级别为DEBUG，启用文件输出和控制台输出
UpLoggerInjection.enableFullLogs(true);

// 禁用完整日志模式（异步执行）
// 恢复日志级别为INFO
UpLoggerInjection.enableFullLogs(false);
```

**注意**: 这些接口对应Android版本的`enableConsoleLog()`和`enableFullLogs()`方法，保持接口一致性。

## 9. 配置功能说明

### 基础配置
- **logLevel**: 日志级别过滤
- **enableConsoleOutput**: 是否启用控制台输出
- **enableFileOutput**: 是否启用文件输出
- **enableFullLog**: 是否开启全量日志（开启后会触发上传）

### 隐私配置
- **privacyAgreed**: 隐私协议同意状态
- **userId**: 用户ID（可简单更新）

### 文件配置
- **maxFileSize**: 单个日志文件最大大小
- **maxDirectorySize**: 整个日志目录大小限制

### 上传配置
- **uploadUrl**: 上传服务器地址
- **enableFullLog**: 控制是否开启全量日志，开启后会自动上传

# 使用示例

## 基础使用示例
```typescript
class EntryLog {
  private static readonly TAG: string = "EntryLog";
  static readonly sLogger: Logger = UpLoggerInjection.provideManager().createLogger(EntryLog.TAG);
}

export function logger(): Logger {
  return EntryLog.sLogger;
}

logger().info("this is info message1");
logger().info("this is info message2 %{public}s", "message2");
logger().info("this is info message3 %{public}s %{public}s", "message3", "message3");
```

## 完整配置示例
```typescript
import { UpLoggerInjection, UpLogConfig, LogLevel } from 'uplog/Index';

// 在应用启动时配置
export default class EntryAbility extends UIAbility {
  onCreate(want: Want, launchParam: AbilityConstant.LaunchParam): void {
    // 创建配置
    const config = UpLogConfig.builder()
      .setLogLevel(LogLevel.DEBUG)

      .setPrivacyAgreed(true)
      .setFileConfig(
        20 * 1024 * 1024,  // 20MB单文件
        200 * 1024 * 1024  // 200MB目录总大小
      )
      .setUploadUrl("https://api.example.com/logs")
      .build();

    // 初始化日志系统
    UpLoggerInjection.initializeWithConfig(this.context, config);

    // 用户登录后更新用户ID
    // UpLoggerInjection.updateUserId("user123");
  }
}
```

## 架构说明

### 架构设计

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     UpLog       │───▶│ logger/ohosApp  │───▶│ logger/rust_*   │
│   (前端接口)    │    │   (桥接层)      │    │   (Rust逻辑)    │
│                 │    │                 │    │                 │
│ • 配置接口      │    │ • RustChannel   │    │ • 统一日志逻辑  │
│ • API兼容       │    │ • FlatBuffer    │    │ • xlog文件管理  │
│ • 格式转换      │    │ • 类型转换      │    │ • protobuf格式  │
│ • 简单传递      │    │ • 异步调用      │    │ • 隐私处理      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### UpLog库职责（本库）
1. **配置管理**: 提供Builder模式的配置接口
2. **API兼容**: 保持现有API的向后兼容性
3. **格式转换**: 自动转换鸿蒙格式为Rust格式
4. **简单传递**: userId更新、隐私状态设置等简单操作
5. **无业务逻辑**: 不包含任何存储、格式化、上传等业务逻辑

### 相关库说明
- **logger/ohosApp**: HarmonyOS桥接层，通过RustChannel与Rust层交互，使用FlatBuffer进行数据序列化
- **logger/rust_logger**: Rust统一逻辑库，使用xlog进行文件管理，支持protobuf序列化、隐私处理等功能
- 详细架构请参考logger库的README文档

### 技术栈说明
- **前端通信**: RustChannel统一接口，替代了原有的UpLogBridge
- **数据序列化**: FlatBuffer格式用于前端与Rust层通信，protobuf格式用于服务端通信
- **异步设计**: 所有配置更新和日志写入都采用异步方式，提供更好的性能