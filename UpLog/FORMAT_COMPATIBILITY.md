# 日志格式化兼容性方案

## 问题描述

1. **Rust格式化语法**: Rust使用`{}`语法，如`"Hello {}"`
2. **鸿蒙格式化语法**: 鸿蒙使用`%{public}s`语法
3. **现有代码兼容**: 已有UpLog用户使用鸿蒙格式，不能破坏兼容性

## 解决方案

### 当前方案：UpLog层格式转换（已实现）

在UpLog层将鸿蒙格式转换为Rust格式：

```typescript
// ArkTS层（保持现有格式）
logger.info("user: %{public}s, count: %{public}d", "username", 123);

// UpLog层自动转换
// "%{public}s" -> "{}"
// "%{private}s" -> "{}"
// "%{public}d" -> "{}"
// "%{public}i" -> "{}"

// 传递给Rust层
rust_log("user: {}, count: {}", ["username", "123"]) // 脱敏由Rust层处理
```

### 其他考虑过的方案

**方案二：Rust层解析鸿蒙格式**
- 让Rust层直接解析鸿蒙格式
- 缺点：增加Rust层复杂度，不如在调用层处理

**方案三：双格式支持**
- 同时支持两种格式
- 缺点：增加维护复杂度，容易混淆

## 当前实现

采用**UpLog层格式转换**：

1. **保持ArkTS层兼容性**: 继续支持`%{public}s`格式
2. **UpLog层转换**: 将格式转换为Rust可识别的格式
3. **脱敏处理**: 完全由Rust层统一处理，使用Android兼容规则

## 数据类型映射

| 鸿蒙格式 | Rust格式 | 类型 | 敏感性 |
|---------|---------|------|--------|
| `%{public}s` | `{}` | String | 非敏感 |
| `%{private}s` | `{}` | String | 敏感 |
| `%{public}d` | `{}` | Number | 非敏感 |
| `%{private}d` | `{}` | Number | 敏感 |
| `%{public}i` | `{}` | BigInt | 非敏感 |

## 实际接口设计

```typescript
interface LogEntry {
  format: string;        // 转换后的Rust格式: "user: {}, count: {}"
  args: any[];          // 参数数组: ["username", 123]
  level: number;        // 日志级别
  tag: string;          // 日志标签
}
```

**注意**: 不再需要sensitive参数，脱敏处理完全由Rust层统一处理。

这样既保持了现有代码的兼容性，又简化了接口设计，让Rust层统一处理格式化和敏感信息脱敏。
