#!/usr/bin/env python3

import sys
import os
import glob
import zlib
import struct
import binascii
import traceback

# 尝试导入zstandard，如果没有安装则提示
try:
    import zstandard as zstd
    HAS_ZSTD = True
except ImportError:
    print("Warning: zstandard not installed. Install with: pip install zstandard")
    HAS_ZSTD = False

MAGIC_NO_COMPRESS_START = 0x03
MAGIC_NO_COMPRESS_START1 = 0x06
MAGIC_NO_COMPRESS_NO_CRYPT_START = 0x08
MAGIC_COMPRESS_START = 0x04
MAGIC_COMPRESS_START1 = 0x05
MAGIC_COMPRESS_START2 = 0x07
MAGIC_COMPRESS_NO_CRYPT_START = 0x09

MAGIC_SYNC_ZSTD_START = 0x0A
MAGIC_SYNC_NO_CRYPT_ZSTD_START = 0x0B
MAGIC_ASYNC_ZSTD_START = 0x0C
MAGIC_ASYNC_NO_CRYPT_ZSTD_START = 0x0D

MAGIC_END = 0x00

lastseq = 0

class ZstdDecompressReader:
    def __init__(self, buffer):
        self.buffer = buffer

    def read(self, size):
        return self.buffer

def IsGoodLogBuffer(_buffer, _offset, count):
    if _offset >= len(_buffer): return False
    
    magic_start = _buffer[_offset]
    if MAGIC_NO_COMPRESS_START != magic_start and \
       MAGIC_NO_COMPRESS_START1 != magic_start and \
       MAGIC_COMPRESS_START != magic_start and \
       MAGIC_COMPRESS_START1 != magic_start and \
       MAGIC_COMPRESS_START2 != magic_start and \
       MAGIC_NO_COMPRESS_NO_CRYPT_START != magic_start and \
       MAGIC_COMPRESS_NO_CRYPT_START != magic_start and \
       MAGIC_SYNC_ZSTD_START != magic_start and \
       MAGIC_SYNC_NO_CRYPT_ZSTD_START != magic_start and \
       MAGIC_ASYNC_ZSTD_START != magic_start and \
       MAGIC_ASYNC_NO_CRYPT_ZSTD_START != magic_start:
        return False
    
    crypt_key_len = 4
    if MAGIC_NO_COMPRESS_NO_CRYPT_START == magic_start or \
       MAGIC_COMPRESS_NO_CRYPT_START == magic_start or \
       MAGIC_SYNC_NO_CRYPT_ZSTD_START == magic_start or \
       MAGIC_ASYNC_NO_CRYPT_ZSTD_START == magic_start:
        crypt_key_len = 0
    
    headerLen = 1 + 2 + 1 + 1 + 4 + crypt_key_len
    if _offset + headerLen + 1 + 1 >= len(_buffer): return False
    
    length = struct.unpack_from("I", _buffer, _offset + 1 + 2 + 1 + 1)[0]
    if _offset + headerLen + length + 1 >= len(_buffer): return False
    if MAGIC_END != _buffer[_offset + headerLen + length]: return False
    
    if 1 >= count:
        return True
    else:
        return IsGoodLogBuffer(_buffer, _offset + headerLen + length + 1, count - 1)

def GetLogStartPos(_buffer, count):
    offset = 0
    print(f"🔍 Searching for log start position in {len(_buffer)} bytes...")

    # 先检查文件头部的magic numbers
    if len(_buffer) > 0:
        print(f"📋 First 16 bytes: {' '.join(f'{b:02x}' for b in _buffer[:16])}")

    while True:
        if offset >= len(_buffer): break
        if IsGoodLogBuffer(_buffer, offset, count):
            print(f"✅ Found valid log start at offset: {offset}")
            return offset
        offset += 1

    print("❌ No valid log start position found")
    print("🔍 Checking for known magic numbers in file:")

    magic_numbers = {
        0x03: "MAGIC_NO_COMPRESS_START",
        0x04: "MAGIC_COMPRESS_START",
        0x05: "MAGIC_COMPRESS_START1",
        0x06: "MAGIC_NO_COMPRESS_START1",
        0x07: "MAGIC_COMPRESS_START2",
        0x08: "MAGIC_NO_COMPRESS_NO_CRYPT_START",
        0x09: "MAGIC_COMPRESS_NO_CRYPT_START",
        0x0A: "MAGIC_SYNC_ZSTD_START",
        0x0B: "MAGIC_SYNC_NO_CRYPT_ZSTD_START",
        0x0C: "MAGIC_ASYNC_ZSTD_START",
        0x0D: "MAGIC_ASYNC_NO_CRYPT_ZSTD_START"
    }

    found_magics = []
    for i, byte in enumerate(_buffer[:1000]):  # 检查前1000字节
        if byte in magic_numbers:
            found_magics.append((i, byte, magic_numbers[byte]))

    if found_magics:
        print("🎯 Found potential magic numbers:")
        for offset, magic, name in found_magics[:10]:  # 只显示前10个
            print(f"   Offset {offset}: 0x{magic:02x} ({name})")
    else:
        print("❌ No known magic numbers found in first 1000 bytes")

    return -1

def DecodeBuffer(_buffer, _offset, _outbuffer):
    if _offset >= len(_buffer): return -1
    if _offset + 1 + 4 + 1 + 1 >= len(_buffer): return -1
    
    # 读取magic number
    magic_start = _buffer[_offset]
    
    # 确定加密密钥长度
    crypt_key_len = 4
    if magic_start in [MAGIC_NO_COMPRESS_NO_CRYPT_START, MAGIC_COMPRESS_NO_CRYPT_START, 
                       MAGIC_SYNC_NO_CRYPT_ZSTD_START, MAGIC_ASYNC_NO_CRYPT_ZSTD_START]:
        crypt_key_len = 0
    
    headerLen = 1 + 2 + 1 + 1 + 4 + crypt_key_len
    
    if _offset + headerLen >= len(_buffer): return -1
    
    # 解析头部信息
    seq, begin_hour, end_hour = struct.unpack_from("HBB", _buffer, _offset + 1)
    length = struct.unpack_from("I", _buffer, _offset + 1 + 2 + 1 + 1)[0]
    
    if _offset + headerLen + length + 1 >= len(_buffer): return -1
    if MAGIC_END != _buffer[_offset + headerLen + length]: return -1
    
    global lastseq
    if seq != 0 and seq != 1 and lastseq != 0 and seq != (lastseq + 1):
        _outbuffer.extend(f"[F]decode_log_file.py log seq:{seq} != lastseq:{lastseq} + 1\n".encode())
    lastseq = seq
    
    try:
        # 提取压缩数据
        tmpbuffer = _buffer[_offset + headerLen:_offset + headerLen + length]
        
        # 根据magic number解压数据
        if magic_start in [MAGIC_NO_COMPRESS_START1, MAGIC_COMPRESS_START2, 
                          MAGIC_SYNC_ZSTD_START, MAGIC_ASYNC_ZSTD_START]:
            print("use wrong decode script")
        elif magic_start == MAGIC_ASYNC_NO_CRYPT_ZSTD_START:
            if not HAS_ZSTD:
                _outbuffer.extend(b"[F]zstandard not available for decompression\n")
                return _offset + headerLen + length + 1
            decompressor = zstd.ZstdDecompressor()
            tmpbuffer = next(decompressor.read_from(ZstdDecompressReader(bytes(tmpbuffer)), 100000, 1000000))
        elif magic_start in [MAGIC_COMPRESS_START, MAGIC_COMPRESS_NO_CRYPT_START]:
            decompressor = zlib.decompressobj(-zlib.MAX_WBITS)
            tmpbuffer = decompressor.decompress(bytes(tmpbuffer))
        elif magic_start == MAGIC_COMPRESS_START1:
            decompress_data = bytearray()
            while len(tmpbuffer) > 0:
                single_log_len = struct.unpack_from("H", tmpbuffer, 0)[0]
                decompress_data.extend(tmpbuffer[2:single_log_len+2])
                tmpbuffer = tmpbuffer[single_log_len+2:]
            
            decompressor = zlib.decompressobj(-zlib.MAX_WBITS)
            tmpbuffer = decompressor.decompress(bytes(decompress_data))
        else:
            pass  # 无压缩数据
            
    except Exception as e:  # Python 3 语法
        traceback.print_exc()  
        _outbuffer.extend(f"[F]decode_log_file.py decompress err, {str(e)}\n".encode())
        return _offset + headerLen + length + 1

    _outbuffer.extend(tmpbuffer)
    
    return _offset + headerLen + length + 1

def ParseFile(_file, _outfile):
    print(f"Parsing: {_file} -> {_outfile}")
    
    try:
        with open(_file, "rb") as fp:
            _buffer = bytearray(fp.read())
    except Exception as e:
        print(f"Error reading file {_file}: {e}")
        return
    
    startpos = GetLogStartPos(_buffer, 2)
    if startpos == -1:
        print(f"No valid log data found in {_file}")
        return
    
    outbuffer = bytearray()
    
    while True:
        startpos = DecodeBuffer(_buffer, startpos, outbuffer)
        if startpos == -1: 
            break
    
    if len(outbuffer) == 0: 
        print(f"No data decoded from {_file}")
        return
    
    try:
        with open(_outfile, "wb") as fpout:
            fpout.write(outbuffer)
        print(f"Successfully decoded to: {_outfile}")
    except Exception as e:
        print(f"Error writing output file {_outfile}: {e}")

def main(args):
    global lastseq

    if len(args) == 1:
        if os.path.isdir(args[0]):
            filelist = glob.glob(os.path.join(args[0], "*.xlog"))
            if not filelist:
                print(f"No .xlog files found in directory: {args[0]}")
                return
            for filepath in filelist:
                lastseq = 0
                ParseFile(filepath, filepath + ".log")
        else: 
            ParseFile(args[0], args[0] + ".log")    
    elif len(args) == 2:
        ParseFile(args[0], args[1])    
    else: 
        filelist = glob.glob("*.xlog")
        if not filelist:
            print("No .xlog files found in current directory")
            return
        for filepath in filelist:
            lastseq = 0
            ParseFile(filepath, filepath + ".log")

if __name__ == "__main__":
    if len(sys.argv) == 1:
        print("Usage:")
        print("  python3 decode_mars_nocrypt_log_file_py3.py <file.xlog>")
        print("  python3 decode_mars_nocrypt_log_file_py3.py <directory>")
        print("  python3 decode_mars_nocrypt_log_file_py3.py <input.xlog> <output.log>")
        print("  python3 decode_mars_nocrypt_log_file_py3.py  # decode all .xlog in current dir")
    else:
        main(sys.argv[1:])
