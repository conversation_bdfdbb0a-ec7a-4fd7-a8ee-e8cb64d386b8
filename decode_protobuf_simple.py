#!/usr/bin/env python3

import sys
import os

# 添加protobuf生成文件的路径
sys.path.insert(0, 'logger/rust_logger/proto')

try:
    import log_metadata_pb2
    print("✅ Successfully imported log_metadata_pb2")
except ImportError as e:
    print(f"❌ Failed to import log_metadata_pb2: {e}")
    sys.exit(1)

def read_varint(data, offset=0):
    """读取protobuf varint"""
    result = 0
    shift = 0
    pos = offset
    
    while pos < len(data):
        byte = data[pos]
        result |= (byte & 0x7F) << shift
        pos += 1
        
        if (byte & 0x80) == 0:
            return result, pos - offset
            
        shift += 7
        if shift >= 64:
            raise ValueError("Varint too long")
    
    raise ValueError("Incomplete varint")

def parse_delimited_messages(data):
    """解析带长度前缀的protobuf消息"""
    messages = []
    offset = 0
    
    print(f"🔍 Parsing {len(data)} bytes of data...")
    
    while offset < len(data):
        try:
            # 尝试读取varint长度
            if offset >= len(data):
                break
                
            length, varint_size = read_varint(data, offset)
            print(f"📏 Found varint length: {length} at offset {offset}")
            
            if length == 0:
                offset += varint_size
                continue
                
            offset += varint_size
            
            # 检查是否有足够的数据
            if offset + length > len(data):
                print(f"⚠️  Warning: Not enough data for message at offset {offset}, need {length}, have {len(data) - offset}")
                break
            
            # 提取消息数据
            message_data = data[offset:offset + length]
            offset += length
            
            print(f"📦 Trying to parse {len(message_data)} bytes as protobuf...")
            
            # 解析protobuf消息
            try:
                log_entry = log_metadata_pb2.LogMetaData()
                log_entry.ParseFromString(message_data)
                messages.append(log_entry)
                print(f"✅ Parsed message {len(messages)}: {log_entry.level} - {log_entry.tag}")
            except Exception as e:
                print(f"❌ Failed to parse protobuf message: {e}")
                print(f"📋 Message data: {' '.join(f'{b:02x}' for b in message_data[:32])}")
                
        except Exception as e:
            print(f"❌ Error at offset {offset}: {e}")
            break
    
    return messages

def try_parse_single_message(data):
    """尝试将整个数据作为单个protobuf消息解析"""
    print(f"🔍 Trying to parse entire {len(data)} bytes as single protobuf message...")
    
    try:
        log_entry = log_metadata_pb2.LogMetaData()
        log_entry.ParseFromString(data)
        print(f"✅ Successfully parsed single message: {log_entry.level} - {log_entry.tag}")
        return [log_entry]
    except Exception as e:
        print(f"❌ Failed to parse as single protobuf message: {e}")
        return []

def try_parse_raw_text(data):
    """尝试将数据作为原始文本解析"""
    print(f"🔍 Trying to parse as raw text...")
    
    try:
        # 尝试不同的编码
        for encoding in ['utf-8', 'latin-1', 'ascii']:
            try:
                text = data.decode(encoding)
                if text.strip():
                    print(f"✅ Successfully decoded as {encoding}:")
                    print(f"📝 Text content: {repr(text[:200])}")
                    return text
            except UnicodeDecodeError:
                continue
        
        print("❌ Failed to decode as text")
        return None
    except Exception as e:
        print(f"❌ Error parsing as text: {e}")
        return None

def main():
    if len(sys.argv) != 2:
        print("Usage: python3 decode_protobuf_simple.py <decoded_log_file>")
        print("Example: python3 decode_protobuf_simple.py /Users/<USER>/Downloads/uplog_20250620.xlog.log")
        sys.exit(1)
    
    log_file = sys.argv[1]
    
    if not os.path.exists(log_file):
        print(f"❌ File not found: {log_file}")
        sys.exit(1)
    
    print(f"🔧 Reading data from: {log_file}")
    
    with open(log_file, 'rb') as f:
        data = f.read()
    
    print(f"📁 File size: {len(data)} bytes")
    print(f"📋 First 32 bytes: {' '.join(f'{b:02x}' for b in data[:32])}")
    print(f"📋 Last 32 bytes: {' '.join(f'{b:02x}' for b in data[-32:])}")
    
    # 尝试不同的解析方法
    print("\n" + "="*60)
    print("🔍 Method 1: Trying delimited message format...")
    messages = parse_delimited_messages(data)
    
    if not messages:
        print("\n" + "="*60)
        print("🔍 Method 2: Trying single message format...")
        messages = try_parse_single_message(data)
    
    if not messages:
        print("\n" + "="*60)
        print("🔍 Method 3: Trying raw text format...")
        text = try_parse_raw_text(data)
        if text:
            output_file = log_file + ".txt"
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(text)
            print(f"💾 Raw text saved to: {output_file}")
            return
    
    if messages:
        print(f"\n🎉 Successfully parsed {len(messages)} log entries:")
        print("=" * 80)
        
        for i, msg in enumerate(messages, 1):
            print(f"\n📝 Log Entry #{i}:")
            print(f"   Time: {msg.time}")
            print(f"   Level: {msg.level}")
            print(f"   Tag: {msg.tag}")
            print(f"   Session ID: {msg.session_id}")
            print(f"   User ID: {msg.user_id}")
            print(f"   Test Mode: {msg.test_mode}")
            print(f"   Message: {msg.log_message}")
            print("-" * 40)
        
        # 保存为可读文本
        output_file = log_file + ".txt"
        with open(output_file, 'w', encoding='utf-8') as f:
            for i, msg in enumerate(messages, 1):
                f.write(f"Log Entry #{i}:\n")
                f.write(f"Time: {msg.time}\n")
                f.write(f"Level: {msg.level}\n")
                f.write(f"Tag: {msg.tag}\n")
                f.write(f"Session ID: {msg.session_id}\n")
                f.write(f"User ID: {msg.user_id}\n")
                f.write(f"Test Mode: {msg.test_mode}\n")
                f.write(f"Message: {msg.log_message}\n")
                f.write("-" * 40 + "\n")
        
        print(f"\n💾 Readable logs saved to: {output_file}")
    else:
        print("\n❌ No valid data found")
        print("💡 The data might be:")
        print("   1. Still compressed (need further decompression)")
        print("   2. Using a different format")
        print("   3. Corrupted")
        print("   4. Not protobuf data")

if __name__ == "__main__":
    main()
